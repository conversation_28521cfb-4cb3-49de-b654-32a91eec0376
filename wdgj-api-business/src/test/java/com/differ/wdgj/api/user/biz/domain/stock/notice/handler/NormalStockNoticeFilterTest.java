package com.differ.wdgj.api.user.biz.domain.stock.notice.handler;

import com.differ.wdgj.api.AbstractSpringTest;
import com.differ.wdgj.api.user.biz.domain.stock.data.StockContentResult;
import com.differ.wdgj.api.user.biz.domain.stock.data.enums.StockMultiWhsModeEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.notice.ApiSysMatchNotice;
import com.differ.wdgj.api.user.biz.domain.stock.data.trigger.StockSyncContext;
import com.differ.wdgj.api.user.biz.domain.stock.multi.adapter.core.IMultiWarehouseAdapter;
import com.differ.wdgj.api.user.biz.domain.stock.notice.erp.processor.handler.filter.NormalStockNoticeFilter;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiSysMatchDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.shop.config.SyncStockNumRuleDto;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.shop.config.SyncStockShopConfig;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;

import java.util.Arrays;

import static org.mockito.Mockito.when;

/**
 * 普通库存同步匹配过滤器
 *
 * <AUTHOR>
 * @date 2025/3/13 下午3:59
 */
public class NormalStockNoticeFilterTest extends AbstractSpringTest {
    //region 变量
    /**
     * 待测试的过滤器实例
     */
    private NormalStockNoticeFilter filter;

    /**
     * 测试数据 - 库存同步上下文
     */
    private StockSyncContext context;

    /**
     * 测试数据 - 系统匹配通知
     */
    private ApiSysMatchNotice notice;

    /**
     * 测试数据 - 匹配信息
     */
    private ApiSysMatchDO matchDO;

    /**
     * 测试数据 - 店铺配置
     */
    private SyncStockShopConfig shopConfig;

    @Mock
    private IMultiWarehouseAdapter multiWhsAdapter;
    //endregion

    //region 初始化
    /**
     * 每个测试方法执行前的初始化工作
     */
    @Before
    public void setUp() throws Exception {

        // 初始化过滤器
        filter = new NormalStockNoticeFilter();

        // 初始化测试数据
        initTestData();
    }

    /**
     * 初始化测试数据
     */
    private void initTestData() {
        // 初始化上下文
        context = new StockSyncContext();

        // 初始化匹配信息
        matchDO = new ApiSysMatchDO();
        matchDO.setId(1);
        matchDO.setShopId(100);
        matchDO.setBstop(false);
        matchDO.setRuleWarehouse("1001,1002");

        // 初始化系统匹配通知
        notice = new ApiSysMatchNotice();
        notice.setPlatGoodsMatch(matchDO);
        notice.setWarehouseId("1001");

        // 初始化店铺配置
        shopConfig = new SyncStockShopConfig();
        SyncStockNumRuleDto numRule = new SyncStockNumRuleDto();
        numRule.setWarehouseIds(Arrays.asList("1001", "1002"));
        shopConfig.setSyncNumRule(numRule);
        context.setSyncStockConfig(shopConfig);

        // 设置多仓适配器
        context.setMultiWhsAdapter(multiWhsAdapter);
        when(multiWhsAdapter.getMode(matchDO)).thenReturn(StockMultiWhsModeEnum.ONLY_NORMAL);
    }
    //endregion

    /**
     * 测试正常流程
     * 预期：所有配置正确时，返回成功
     */
    @Test
    public void testProcessSuccess() {
        StockContentResult<?> result = filter.process(context, notice);
        Assert.assertTrue("正常流程应该返回成功", result.getSuccess());
    }

    /**
     * 测试匹配信息为空场景
     * 预期：返回失败，提示数据不完整
     */
    @Test
    public void testProcessWithNullMatch() {
        notice.setPlatGoodsMatch(null);

        StockContentResult<?> result = filter.process(context, notice);
        Assert.assertFalse("匹配信息为空应该返回失败", result.getSuccess());
        Assert.assertEquals("错误信息不符合预期", "匹配记录数据不完整", result.getMessage());
    }

    /**
     * 测试店铺ID为0 场景
     * 预期：返回失败，提示数据不完整
     */
    @Test
    public void testProcessWithInvalidShopId() {
        matchDO.setShopId(0);

        StockContentResult<?> result = filter.process(context, notice);
        Assert.assertFalse("无效店铺ID应该返回失败", result.getSuccess());
        Assert.assertEquals("错误信息不符合预期", "匹配记录数据不完整", result.getMessage());
    }

    /**
     * 测试匹配已停用场景
     * 预期：返回失败，提示匹配已停用
     */
    @Test
    public void testProcessWithStoppedMatch() {
        matchDO.setBstop(true);

        StockContentResult<?> result = filter.process(context, notice);
        Assert.assertFalse("已停用匹配应该返回失败", result.getSuccess());
        Assert.assertTrue("错误信息应包含停用说明",
                result.getMessage().contains("已停用"));
    }

    /**
     * 测试仅支持多仓库场景
     * 预期：返回失败，提示仅支持多仓库
     */
    @Test
    public void testProcessWithOnlyMultiWarehouse() {
        when(multiWhsAdapter.getMode(matchDO)).thenReturn(StockMultiWhsModeEnum.ONLY_MULTI_WAREHOUSE);

        StockContentResult<?> result = filter.process(context, notice);
        Assert.assertFalse("仅支持多仓库时应该返回失败", result.getSuccess());
        Assert.assertTrue("错误信息应包含仓库模式说明",
                result.getMessage().contains("仅支持多仓库存同步"));
    }

    /**
     * 测试仓库不在匹配配置中场景
     * 预期：返回失败，提示仓库不在配置中
     */
    @Test
    public void testProcessWithWarehouseNotInMatchConfig() {
        notice.setWarehouseId("1003");

        StockContentResult<?> result = filter.process(context, notice);
        Assert.assertFalse("仓库不在匹配配置中应该返回失败", result.getSuccess());
        Assert.assertTrue("错误信息应包含仓库配置说明",
                result.getMessage().contains("不在匹配配置中"));
    }

    /**
     * 测试仓库不在店铺配置中场景
     * 预期：返回失败，提示仓库不在店铺配置中
     */
    @Test
    public void testProcessWithWarehouseNotInShopConfig() {
        shopConfig.getSyncNumRule().setWarehouseIds(Arrays.asList("1002"));

        StockContentResult<?> result = filter.process(context, notice);
        Assert.assertFalse("仓库不在店铺配置中应该返回失败", result.getSuccess());
        Assert.assertTrue("错误信息应包含仓库配置说明",
                result.getMessage().contains("不在店铺配置中"));
    }
}
