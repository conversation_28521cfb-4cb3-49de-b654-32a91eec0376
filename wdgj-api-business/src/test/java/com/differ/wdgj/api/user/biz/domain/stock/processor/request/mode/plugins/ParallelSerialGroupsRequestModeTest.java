package com.differ.wdgj.api.user.biz.domain.stock.processor.request.mode.plugins;

import com.differ.wdgj.api.user.biz.domain.apicall.data.ApiCallResponse;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.batchSyncStock.BusinessBatchSyncStockRequestGoodInfo;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.batchSyncStock.BusinessBatchSyncStockResponseGoodSyncStockResultItem;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.batchSyncStock.PolyAPIBusinessBatchSyncStockRequest;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.batchSyncStock.PolyAPIBusinessBatchSyncStockResponse;
import com.differ.wdgj.api.user.biz.domain.apicall.plugins.stock.BatchSyncStockApiCall;
import com.differ.wdgj.api.user.biz.domain.stock.data.GoodsMatchEnhance;
import com.differ.wdgj.api.user.biz.domain.stock.data.StockContentResult;
import com.differ.wdgj.api.user.biz.domain.stock.data.request.StockSyncRequestGroup;
import com.differ.wdgj.api.user.biz.domain.stock.data.request.StockSyncRequestSerialGroup;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.GoodsStockCalculationResult;
import com.differ.wdgj.api.user.biz.domain.stock.data.trigger.StockSyncContext;
import com.differ.wdgj.api.user.biz.domain.stock.data.trigger.StockSyncGoodsRequestPair;
import com.differ.wdgj.api.user.biz.domain.stock.data.trigger.StockSyncMatchExecuteResult;
import com.differ.wdgj.api.user.biz.domain.stock.data.trigger.StockSyncRequestItem;
import com.differ.wdgj.api.user.biz.domain.stock.processor.BaseSyncStockProcessor;
import com.differ.wdgj.api.user.biz.domain.stock.processor.request.mode.plugins.ParallelSerialGroupsRequestMode;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiSysMatchDO;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Future;

import static org.junit.Assert.*;
import static org.mockito.Matchers.any;
import static org.mockito.Matchers.eq;
import static org.mockito.Mockito.when;

/**
 * 并行请求串行列表模式
 */
public class ParallelSerialGroupsRequestModeTest {

    //region 变量
    @Mock
    private StockSyncContext mockContext;

    @Mock
    private BaseSyncStockProcessor mockProcessor;

    @Mock
    private BatchSyncStockApiCall mockStockApiCall;

    private ParallelSerialGroupsRequestMode testMode;
    //endregion

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        
        // 模拟通用的上下文数据
        when(mockContext.getVipUser()).thenReturn("testUser");
        when(mockContext.getShopId()).thenReturn(1000);
        
        testMode = new ParallelSerialGroupsRequestMode(mockContext, mockProcessor, mockStockApiCall);
    }

    /**
     * 测试doRequest方法 - 空串行组列表场景
     * 验证：
     * 1. 返回成功状态
     * 2. 返回空列表
     */
    @Test
    public void testDoRequest_EmptySerialGroups() {
        StockSyncRequestGroup group = new StockSyncRequestGroup();
        StockContentResult<List<Future<List<StockSyncMatchExecuteResult>>>> result = testMode.doRequest(group);

        assertNotNull(result);
        assertTrue(result.getSuccess());
        assertTrue(result.getContent().isEmpty());
    }

    /**
     * 测试doRequest方法 - 多个串行组场景
     * 验证：
     * 1. 返回成功状态
     * 2. 返回的Future列表大小与串行组数量相同
     */
    @Test
    public void testDoRequest_MultipleSerialGroups() {
        // 准备测试数据
        StockSyncRequestGroup group = new StockSyncRequestGroup();
        StockSyncRequestSerialGroup serialGroup1 = new StockSyncRequestSerialGroup();
        StockSyncRequestSerialGroup serialGroup2 = new StockSyncRequestSerialGroup();

        StockSyncRequestItem requestItem1 = createTestRequestItem();
        StockSyncRequestItem requestItem2 = createTestRequestItem();

        serialGroup1.setRequests(Collections.singletonList(requestItem1));
        serialGroup2.setRequests(Collections.singletonList(requestItem2));

        group.setSerialGroups(Arrays.asList(serialGroup1, serialGroup2));

        // 模拟API调用返回成功响应
        ApiCallResponse<PolyAPIBusinessBatchSyncStockResponse> response = createSuccessResponse();
        assertNotNull(mockStockApiCall);
        assertNotNull(response);
        when(mockStockApiCall.apiCall(eq("testUser"), eq(1000), any())).thenReturn(response);
        when(mockProcessor.platMatchGoodsResponse(any(), any())).thenReturn(true);

        // 执行测试
        StockContentResult<List<Future<List<StockSyncMatchExecuteResult>>>> result = testMode.doRequest(group);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.getSuccess());
        assertEquals(2, result.getContent().size());
    }

    /**
     * 测试doRequest方法 - 串行组内多个请求场景
     * 验证：
     * 1. 返回成功状态
     * 2. 返回的Future列表大小为1（一个串行组）
     */
    @Test
    public void testDoRequest_MultipleRequestsInSerialGroup() {
        // 准备测试数据
        StockSyncRequestGroup group = new StockSyncRequestGroup();
        StockSyncRequestSerialGroup serialGroup = new StockSyncRequestSerialGroup();

        StockSyncRequestItem requestItem1 = createTestRequestItem();
        StockSyncRequestItem requestItem2 = createTestRequestItem();

        serialGroup.setRequests(Arrays.asList(requestItem1, requestItem2));
        group.setSerialGroups(Collections.singletonList(serialGroup));

        // 模拟API调用返回成功响应
        ApiCallResponse<PolyAPIBusinessBatchSyncStockResponse> response = createSuccessResponse();
        assertNotNull(mockStockApiCall);
        assertNotNull(response);
        when(mockStockApiCall.apiCall(eq("testUser"), eq(1000), any())).thenReturn(response);
        when(mockProcessor.platMatchGoodsResponse(any(), any())).thenReturn(true);

        // 执行测试
        StockContentResult<List<Future<List<StockSyncMatchExecuteResult>>>> result = testMode.doRequest(group);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.getSuccess());
        assertEquals(1, result.getContent().size());
    }

    //region 私有方法
    private StockSyncRequestItem createTestRequestItem() {
        PolyAPIBusinessBatchSyncStockRequest request = new PolyAPIBusinessBatchSyncStockRequest();
        BusinessBatchSyncStockRequestGoodInfo goodsInfo = new BusinessBatchSyncStockRequestGoodInfo();
        goodsInfo.setPlatProductId("test_product_id");
        goodsInfo.setSkuId("test_sku_id");

        ApiSysMatchDO sysMatch = new ApiSysMatchDO();
        sysMatch.setId(1);
        sysMatch.setNumiid("test_product_id");
        sysMatch.setSkuID("test_sku_id");
        GoodsMatchEnhance matchEnhance = GoodsMatchEnhance.create(sysMatch, "test_multi_sign");

        GoodsStockCalculationResult goodsStockInfo = new GoodsStockCalculationResult();
        goodsStockInfo.setStockCount(new BigDecimal("100"));

        StockSyncRequestItem stockSyncRequestItem = new StockSyncRequestItem(request);
        stockSyncRequestItem.addGoodsRequest(new StockSyncGoodsRequestPair(matchEnhance, goodsStockInfo, goodsInfo));
        return stockSyncRequestItem;
    }

    private ApiCallResponse<PolyAPIBusinessBatchSyncStockResponse> createSuccessResponse() {
        ApiCallResponse<PolyAPIBusinessBatchSyncStockResponse> response = new ApiCallResponse<>();
        PolyAPIBusinessBatchSyncStockResponse bizData = new PolyAPIBusinessBatchSyncStockResponse();

        BusinessBatchSyncStockResponseGoodSyncStockResultItem resultItem = new BusinessBatchSyncStockResponseGoodSyncStockResultItem();
        resultItem.setPlatProductId("test_product_id");
        resultItem.setSkuId("test_sku_id");
        resultItem.setQuantity(100);

        List<BusinessBatchSyncStockResponseGoodSyncStockResultItem> results = new ArrayList<>();
        results.add(resultItem);
        bizData.setResults(results);

        response.setBizData(bizData);
        return response;
    }
    //endregion
}
