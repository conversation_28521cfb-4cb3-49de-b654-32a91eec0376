package com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.core;

import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.aftersale.PolyRefundGoodsStatusEnum;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.aftersale.PolyRefundTypeEnum;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.order.PolyOrderStatusEnum;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetRefundOrderResponseOrderItem;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetRefundResponseRefundGoodInfo;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * BusinessGetRefundOrderResponseOrderItem 随机数据生成器
 * 用于生成符合电商业务场景的退货退款单测试数据
 *
 * <AUTHOR>
 * @date 2024/12/19 下午8:00
 */
public class BusinessGetRefundOrderResponseOrderItemGenerator {

    //region 常量定义

    /**
     * 随机数生成器
     */
    private static final Random RANDOM = new Random();

    /**
     * 日期时间格式化器
     */
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");

    /**
     * 买家昵称前缀池
     */
    private static final String[] BUYER_NICK_PREFIXES = {
            "tb_", "user_", "buyer_", "客户_", "用户_", "买家_", "会员_"
    };

    /**
     * 卖家昵称池
     */
    private static final String[] SELLER_NICKS = {
            "官方旗舰店", "品牌专营店", "优选商城", "精品小店", "全球购", "海外直营",
            "数码专营", "服装旗舰", "美妆专柜", "母婴用品", "家居生活", "运动户外"
    };

    /**
     * 商品名称池
     */
    private static final String[] PRODUCT_NAMES = {
            "iPhone 15 Pro Max 256GB", "华为Mate60 Pro", "小米14 Ultra", "OPPO Find X7",
            "Nike Air Jordan 1", "Adidas Ultra Boost", "New Balance 990v5", "Converse Chuck Taylor",
            "雅诗兰黛小棕瓶精华", "兰蔻小黑瓶精华", "SK-II神仙水", "资生堂红腰子精华",
            "戴森V15吸尘器", "小米扫地机器人", "美的空气炸锅", "九阳豆浆机",
            "优衣库羽绒服", "ZARA连衣裙", "H&M毛衣", "GAP牛仔裤"
    };

    /**
     * SKU规格池
     */
    private static final String[] SKU_SPECS = {
            "颜色:黑色;尺寸:XL", "颜色:白色;尺寸:L", "颜色:红色;尺寸:M", "颜色:蓝色;尺寸:S",
            "容量:256GB;颜色:深空灰", "容量:128GB;颜色:银色", "容量:512GB;颜色:金色",
            "尺码:42;颜色:黑白", "尺码:40;颜色:全白", "尺码:44;颜色:红黑"
    };

    /**
     * 退款原因池
     */
    private static final String[] REFUND_REASONS = {
            "商品质量问题", "商品与描述不符", "收到商品破损", "尺寸不合适",
            "颜色不喜欢", "买错了", "不需要了", "发错货了",
            "物流损坏", "包装破损", "功能故障", "性能不达标"
    };

    /**
     * 物流公司池
     */
    private static final String[] LOGISTICS_COMPANIES = {
            "顺丰速运", "圆通快递", "中通快递", "申通快递", "韵达快递",
            "百世快递", "德邦快递", "京东物流", "菜鸟网络", "邮政EMS"
    };

    /**
     * 物流公司编码池
     */
    private static final String[] LOGISTICS_CODES = {
            "SF", "YTO", "ZTO", "STO", "YUNDA",
            "BEST", "DEPPON", "JD", "CAINIAO", "EMS"
    };

    /**
     * 省份池
     */
    private static final String[] PROVINCES = {
            "北京市", "上海市", "广东省", "浙江省", "江苏省", "山东省",
            "河南省", "四川省", "湖北省", "湖南省", "福建省", "安徽省"
    };

    /**
     * 城市池
     */
    private static final String[] CITIES = {
            "北京市", "上海市", "广州市", "深圳市", "杭州市", "南京市",
            "济南市", "郑州市", "成都市", "武汉市", "长沙市", "福州市"
    };

    /**
     * 区域池
     */
    private static final String[] AREAS = {
            "朝阳区", "海淀区", "浦东新区", "黄浦区", "天河区", "福田区",
            "西湖区", "拱墅区", "玄武区", "鼓楼区", "历下区", "市中区"
    };

    //endregion

    //region 公共方法

    /**
     * 生成随机的BusinessGetRefundOrderResponseOrderItem
     *
     * @return 随机生成的退货退款单对象
     */
    public static BusinessGetRefundOrderResponseOrderItem generateRandom() {
        return generateRandom(null);
    }

    /**
     * 生成随机的BusinessGetRefundOrderResponseOrderItem
     * 如果传入的template不为空，则不为空的属性不会被随机生成
     *
     * @param template 模板对象，不为空的属性将被保留
     * @return 随机生成的退货退款单对象
     */
    public static BusinessGetRefundOrderResponseOrderItem generateRandom(BusinessGetRefundOrderResponseOrderItem template) {
        BusinessGetRefundOrderResponseOrderItem item = new BusinessGetRefundOrderResponseOrderItem();

        // 必定随机生成的字段
        item.setRefundNo(generateRandomRefundNo());
        item.setPlatOrderNo(generateRandomPlatOrderNo());

        // 基础信息
        setIfNull(item::setTotalAmount, item.getTotalAmount(), template, BusinessGetRefundOrderResponseOrderItem::getTotalAmount,
                () -> generateRandomAmount(50, 5000));
        setIfNull(item::setRefundAmount, item.getRefundAmount(), template, BusinessGetRefundOrderResponseOrderItem::getRefundAmount,
                () -> generateRandomAmount(10, item.getTotalAmount() != null ? item.getTotalAmount() : new BigDecimal("1000")));
        setIfNull(item::setPayAmount, item.getPayAmount(), template, BusinessGetRefundOrderResponseOrderItem::getPayAmount,
                () -> item.getTotalAmount() != null ? item.getTotalAmount() : generateRandomAmount(50, 5000));

        // 买家信息
        setIfNull(item::setBuyerNick, item.getBuyerNick(), template, BusinessGetRefundOrderResponseOrderItem::getBuyerNick,
                () -> generateRandomBuyerNick());
        setIfNull(item::setBuyerOpenUid, item.getBuyerOpenUid(), template, BusinessGetRefundOrderResponseOrderItem::getBuyerOpenUid,
                () -> generateRandomOpenUid());

        // 时间信息
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime createTime = now.minusDays(RANDOM.nextInt(30)).minusHours(RANDOM.nextInt(24));
        LocalDateTime updateTime = createTime.plusHours(RANDOM.nextInt(48));

        setIfNull(item::setCreateTime, item.getCreateTime(), template, BusinessGetRefundOrderResponseOrderItem::getCreateTime,
                () -> createTime);
        setIfNull(item::setUpdateTime, item.getUpdateTime(), template, BusinessGetRefundOrderResponseOrderItem::getUpdateTime,
                () -> updateTime);
        setIfNull(item::setRdsCreateTime, item.getRdsCreateTime(), template, BusinessGetRefundOrderResponseOrderItem::getRdsCreateTime,
                () -> createTime.plusMinutes(RANDOM.nextInt(60)));
        setIfNull(item::setRdsModifyTime, item.getRdsModifyTime(), template, BusinessGetRefundOrderResponseOrderItem::getRdsModifyTime,
                () -> updateTime.plusMinutes(RANDOM.nextInt(60)));

        // 订单信息
        setIfNull(item::setSubPlatOrderNo, item.getSubPlatOrderNo(), template, BusinessGetRefundOrderResponseOrderItem::getSubPlatOrderNo,
                () -> item.getPlatOrderNo() + "-" + (RANDOM.nextInt(9) + 1));

        // 售后类型
        PolyRefundTypeEnum[] values = {PolyRefundTypeEnum.JH_04, PolyRefundTypeEnum.JH_03};
        PolyRefundTypeEnum refundType = getRandomEnum(values);
        setIfNull(item::setRefundType, item.getRefundType(), template, BusinessGetRefundOrderResponseOrderItem::getRefundType,
                () -> refundType.getCode());
        setIfNull(item::setRefundTypeDesc, item.getRefundTypeDesc(), template, BusinessGetRefundOrderResponseOrderItem::getRefundTypeDesc,
                () -> refundType.getDescription());

        // 订单状态
        PolyOrderStatusEnum orderStatus = getRandomEnum(PolyOrderStatusEnum.values(),
                PolyOrderStatusEnum.TRADE_SUCCESS, PolyOrderStatusEnum.WAITING_BUYER_CONFIRM, PolyOrderStatusEnum.TRADE_CLOSED);
        setIfNull(item::setOrderStatus, item.getOrderStatus(), template, BusinessGetRefundOrderResponseOrderItem::getOrderStatus,
                () -> orderStatus.getCode());
        setIfNull(item::setOrderStatusDesc, item.getOrderStatusDesc(), template, BusinessGetRefundOrderResponseOrderItem::getOrderStatusDesc,
                () -> orderStatus.getDescription());

        // 退款状态
        String[] refundStatuses = {"SUCCESS", "PROCESSING", "FAILED", "PENDING", "CLOSED"};
        String[] refundStatusDescs = {"退款成功", "退款处理中", "退款失败", "退款待处理", "退款关闭"};
        int statusIndex = RANDOM.nextInt(refundStatuses.length);
        setIfNull(item::setRefundStatus, item.getRefundStatus(), template, BusinessGetRefundOrderResponseOrderItem::getRefundStatus,
                () -> refundStatuses[statusIndex]);
        setIfNull(item::setRefundStatusDesc, item.getRefundStatusDesc(), template, BusinessGetRefundOrderResponseOrderItem::getRefundStatusDesc,
                () -> refundStatusDescs[statusIndex]);

        // 商品状态
        PolyRefundGoodsStatusEnum goodsStatus = getRandomEnum(PolyRefundGoodsStatusEnum.values());
        setIfNull(item::setGoodsStatus, item.getGoodsStatus(), template, BusinessGetRefundOrderResponseOrderItem::getGoodsStatus,
                () -> goodsStatus.getCode());
        setIfNull(item::setGoodsStatusDesc, item.getGoodsStatusDesc(), template, BusinessGetRefundOrderResponseOrderItem::getGoodsStatusDesc,
                () -> goodsStatus.getDescription());

        // 退货标识
        boolean needReturn = StringUtils.equalsIgnoreCase(item.getRefundType(), PolyRefundTypeEnum.JH_04.getCode()) || StringUtils.equalsIgnoreCase(item.getRefundType(), PolyRefundTypeEnum.JH_05.getCode());
        setIfNull(item::setHasGoodsReturn, item.getHasGoodsReturn(), template, BusinessGetRefundOrderResponseOrderItem::getHasGoodsReturn,
                () -> needReturn);

        // 退款原因和描述
        String reason = getRandomElement(REFUND_REASONS);
        setIfNull(item::setReason, item.getReason(), template, BusinessGetRefundOrderResponseOrderItem::getReason,
                () -> reason);
        setIfNull(item::setDesc, item.getDesc(), template, BusinessGetRefundOrderResponseOrderItem::getDesc,
                () -> "客户反馈：" + reason + "，申请退款处理。");

        // 物流信息（仅在需要退货时生成）
        if (needReturn) {
            int logisticsIndex = RANDOM.nextInt(LOGISTICS_COMPANIES.length);
            setIfNull(item::setLogisticName, item.getLogisticName(), template, BusinessGetRefundOrderResponseOrderItem::getLogisticName,
                    () -> LOGISTICS_COMPANIES[logisticsIndex]);
            setIfNull(item::setLogisticCode, item.getLogisticCode(), template, BusinessGetRefundOrderResponseOrderItem::getLogisticCode,
                    () -> LOGISTICS_CODES[logisticsIndex]);
            setIfNull(item::setLogisticNo, item.getLogisticNo(), template, BusinessGetRefundOrderResponseOrderItem::getLogisticNo,
                    () -> generateRandomLogisticsNo());
        }

        // 店铺信息
        setIfNull(item::setShopId, item.getShopId(), template, BusinessGetRefundOrderResponseOrderItem::getShopId,
                () -> String.valueOf(RANDOM.nextInt(9000) + 1000));
        setIfNull(item::setShopType, item.getShopType(), template, BusinessGetRefundOrderResponseOrderItem::getShopType,
                () -> getRandomElement(new String[]{""}));

        // 地址信息
        generateAddressInfo(item, template);

        // 卖家信息
        setIfNull(item::setSellerNick, item.getSellerNick(), template, BusinessGetRefundOrderResponseOrderItem::getSellerNick,
                () -> getRandomElement(SELLER_NICKS));

        // 商品信息
        generateProductInfo(item, template);

        // 退款商品明细
        setIfNull(item::setRefundGoods, item.getRefundGoods(), template, BusinessGetRefundOrderResponseOrderItem::getRefundGoods,
                () -> generateRefundGoods(item));

        // 扩展字段
        setIfNull(item::setIsSuccess, item.getIsSuccess(), template, BusinessGetRefundOrderResponseOrderItem::getIsSuccess,
                () -> "true");
        setIfNull(item::setSubCode, item.getSubCode(), template, BusinessGetRefundOrderResponseOrderItem::getSubCode,
                () -> "SUCCESS");
        setIfNull(item::setSubMessage, item.getSubMessage(), template, BusinessGetRefundOrderResponseOrderItem::getSubMessage,
                () -> "操作成功");

        return item;
    }

    //endregion

    //region 私有方法

    /**
     * 生成随机退款单号
     * 格式：RF + yyyyMMddHHmmss + 4位随机数
     */
    private static String generateRandomRefundNo() {
        String timestamp = LocalDateTime.now().format(DATE_TIME_FORMATTER);
        int randomSuffix = RANDOM.nextInt(9000) + 1000;
        return "RF" + timestamp + randomSuffix;
    }

    /**
     * 生成随机平台订单号
     * 格式：PO + yyyyMMddHHmmss + 4位随机数
     */
    private static String generateRandomPlatOrderNo() {
        String timestamp = LocalDateTime.now().format(DATE_TIME_FORMATTER);
        int randomSuffix = RANDOM.nextInt(9000) + 1000;
        return "PO" + timestamp + randomSuffix;
    }

    /**
     * 生成随机金额
     */
    private static BigDecimal generateRandomAmount(double min, double max) {
        double amount = min + (max - min) * RANDOM.nextDouble();
        return BigDecimal.valueOf(amount).setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 生成随机金额（基于最大值）
     */
    private static BigDecimal generateRandomAmount(double min, BigDecimal maxAmount) {
        double max = maxAmount.doubleValue();
        return generateRandomAmount(min, max);
    }

    /**
     * 生成随机买家昵称
     */
    private static String generateRandomBuyerNick() {
        String prefix = getRandomElement(BUYER_NICK_PREFIXES);
        String suffix = String.valueOf(RANDOM.nextInt(900000) + 100000);
        return prefix + suffix;
    }

    /**
     * 生成随机OpenUID
     */
    private static String generateRandomOpenUid() {
        return "uid_" + UUID.randomUUID().toString().replace("-", "").substring(0, 16);
    }

    /**
     * 生成随机物流单号
     */
    private static String generateRandomLogisticsNo() {
        StringBuilder sb = new StringBuilder();
        // 物流单号通常是数字和字母的组合
        for (int i = 0; i < 12; i++) {
            if (RANDOM.nextBoolean()) {
                sb.append((char) ('A' + RANDOM.nextInt(26)));
            } else {
                sb.append(RANDOM.nextInt(10));
            }
        }
        return sb.toString();
    }

    /**
     * 生成地址信息
     */
    private static void generateAddressInfo(BusinessGetRefundOrderResponseOrderItem item, BusinessGetRefundOrderResponseOrderItem template) {
        String province = getRandomElement(PROVINCES);
        String city = getRandomElement(CITIES);
        String area = getRandomElement(AREAS);
        String detailAddress = area + "某某街道" + (RANDOM.nextInt(999) + 1) + "号";

        setIfNull(item::setAddress, item.getAddress(), template, BusinessGetRefundOrderResponseOrderItem::getAddress,
                () -> province + city + detailAddress);

        // 卖家收货地址
        setIfNull(item::setSellerReceiveProvince, item.getSellerReceiveProvince(), template, BusinessGetRefundOrderResponseOrderItem::getSellerReceiveProvince,
                () -> province);
        setIfNull(item::setSellerReceiveCity, item.getSellerReceiveCity(), template, BusinessGetRefundOrderResponseOrderItem::getSellerReceiveCity,
                () -> city);
        setIfNull(item::setSellerReceiveArea, item.getSellerReceiveArea(), template, BusinessGetRefundOrderResponseOrderItem::getSellerReceiveArea,
                () -> area);
        setIfNull(item::setSellerAddress, item.getSellerAddress(), template, BusinessGetRefundOrderResponseOrderItem::getSellerAddress,
                () -> province + city + area + "商家收货地址" + (RANDOM.nextInt(99) + 1) + "号");
    }

    /**
     * 生成商品信息
     */
    private static void generateProductInfo(BusinessGetRefundOrderResponseOrderItem item, BusinessGetRefundOrderResponseOrderItem template) {
        String productName = getRandomElement(PRODUCT_NAMES);
        int productNum = RANDOM.nextInt(5) + 1;
        BigDecimal price = generateRandomAmount(10, 2000);

        setIfNull(item::setProductName, item.getProductName(), template, BusinessGetRefundOrderResponseOrderItem::getProductName,
                () -> productName);
        setIfNull(item::setProductNum, item.getProductNum(), template, BusinessGetRefundOrderResponseOrderItem::getProductNum,
                () -> productNum);
        setIfNull(item::setPlatProductId, item.getPlatProductId(), template, BusinessGetRefundOrderResponseOrderItem::getPlatProductId,
                () -> "PROD_" + (RANDOM.nextInt(900000) + 100000));
        setIfNull(item::setSku, item.getSku(), template, BusinessGetRefundOrderResponseOrderItem::getSku,
                () -> getRandomElement(SKU_SPECS));
        setIfNull(item::setOuterId, item.getOuterId(), template, BusinessGetRefundOrderResponseOrderItem::getOuterId,
                () -> "OUTER_" + (RANDOM.nextInt(90000) + 10000));
        setIfNull(item::setPrice, item.getPrice(), template, BusinessGetRefundOrderResponseOrderItem::getPrice,
                () -> price);
        setIfNull(item::setWhsecode, item.getWhsecode(), template, BusinessGetRefundOrderResponseOrderItem::getWhsecode,
                () -> "WH" + String.format("%03d", RANDOM.nextInt(999) + 1));
        setIfNull(item::setWhsecodedesc, item.getWhsecodedesc(), template, BusinessGetRefundOrderResponseOrderItem::getWhsecodedesc,
                () -> "仓库" + (RANDOM.nextInt(10) + 1) + "号");
    }

    /**
     * 生成退款商品明细
     */
    private static List<BusinessGetRefundResponseRefundGoodInfo> generateRefundGoods(BusinessGetRefundOrderResponseOrderItem item) {
        List<BusinessGetRefundResponseRefundGoodInfo> refundGoods = new ArrayList<>();

        // 生成1-3个退款商品
        int goodsCount = RANDOM.nextInt(3) + 1;
        for (int i = 0; i < goodsCount; i++) {
            BusinessGetRefundResponseRefundGoodInfo goodInfo = new BusinessGetRefundResponseRefundGoodInfo();

            goodInfo.setPlatProductId(item.getPlatProductId() + "_" + (i + 1));
            goodInfo.setSku(getRandomElement(SKU_SPECS));
            goodInfo.setOuterId("OUTER_" + (RANDOM.nextInt(90000) + 10000));
            goodInfo.setOutSkuId("OUTER_SKU_" + (RANDOM.nextInt(90000) + 10000));
            goodInfo.setSkuSpec(goodInfo.getSku());
            goodInfo.setProductName(getRandomElement(PRODUCT_NAMES));
            goodInfo.setRefundAmount(generateRandomAmount(10, 500));
            goodInfo.setPrice(generateRandomAmount(50, 1000));
            goodInfo.setReason(getRandomElement(REFUND_REASONS));
            goodInfo.setProductNum(RANDOM.nextInt(3) + 1);
            goodInfo.setRefundProductNum(goodInfo.getProductNum());
            goodInfo.setSubTradeNo(item.getSubPlatOrderNo() + "_" + (i + 1));
            goodInfo.setRefundStatus(getRandomEnum(PolyRefundGoodsStatusEnum.values()).getCode());
            goodInfo.setIsGift(RANDOM.nextDouble() < 0.1); // 10%概率是赠品
            goodInfo.setWhsecode(item.getWhsecode());

            refundGoods.add(goodInfo);
        }

        return refundGoods;
    }

    /**
     * 从数组中随机选择一个元素
     */
    private static <T> T getRandomElement(T[] array) {
        return array[RANDOM.nextInt(array.length)];
    }

    /**
     * 从枚举中随机选择一个值，可以指定偏好的枚举值
     */
    @SafeVarargs
    private static <T extends Enum<T>> T getRandomEnum(T[] values, T... preferred) {
        // 70%概率选择偏好的枚举值
        if (preferred.length > 0 && RANDOM.nextDouble() < 0.7) {
            return getRandomElement(preferred);
        }
        return getRandomElement(values);
    }

    /**
     * 条件设置值的通用方法
     */
    private static <T> void setIfNull(java.util.function.Consumer<T> setter, T currentValue,
                                      BusinessGetRefundOrderResponseOrderItem template,
                                      java.util.function.Function<BusinessGetRefundOrderResponseOrderItem, T> templateGetter,
                                      java.util.function.Supplier<T> randomGenerator) {
        T valueToSet = null;

        // 如果当前值不为空，使用当前值
        if (currentValue != null && !isEmptyValue(currentValue)) {
            valueToSet = currentValue;
        }
        // 如果模板不为空且模板中该字段不为空，使用模板值
        else if (template != null) {
            T templateValue = templateGetter.apply(template);
            if (templateValue != null && !isEmptyValue(templateValue)) {
                valueToSet = templateValue;
            }
        }

        // 如果仍然为空，使用随机生成的值
        if (valueToSet == null || isEmptyValue(valueToSet)) {
            valueToSet = randomGenerator.get();
        }

        setter.accept(valueToSet);
    }

    /**
     * 判断值是否为空
     */
    private static boolean isEmptyValue(Object value) {
        if (value == null) {
            return true;
        }
        if (value instanceof String) {
            return StringUtils.isBlank((String) value);
        }
        if (value instanceof Collection) {
            return ((Collection<?>) value).isEmpty();
        }
        if (value instanceof Map) {
            return ((Map<?, ?>) value).isEmpty();
        }
        return false;
    }

    //endregion
}
