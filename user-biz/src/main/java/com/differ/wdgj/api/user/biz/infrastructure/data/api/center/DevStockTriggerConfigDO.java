package com.differ.wdgj.api.user.biz.infrastructure.data.api.center;

import com.differ.wdgj.api.user.biz.domain.stock.data.enums.StockSyncTriggerConfigTypeEnum;

import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 库存触发配置表
 *
 * <AUTHOR> Generated
 * @date 2024-12-19
 */
@Table(name = "dev_stock_trigger_config")
public class DevStockTriggerConfigDO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID，自增
     */
    private Integer id;

    /**
     * 配置类型，1 通用 2 多仓
     * {@link StockSyncTriggerConfigTypeEnum}
     */
    private Integer configType;

    /**
     * 会员名，多个用 # 分隔，可配置 ALL
     */
    private String memberName;

    /**
     * 触发数量
     */
    private Integer triggerCount;

    /**
     * 降频系数
     */
    private BigDecimal controlFactor;

    /**
     * 备注
     */
    private String remark;

    /**
     * 排序序号
     */
    private Integer sortNo;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getConfigType() {
        return configType;
    }

    public void setConfigType(Integer configType) {
        this.configType = configType;
    }

    public String getMemberName() {
        return memberName;
    }

    public void setMemberName(String memberName) {
        this.memberName = memberName;
    }

    public Integer getTriggerCount() {
        return triggerCount;
    }

    public void setTriggerCount(Integer triggerCount) {
        this.triggerCount = triggerCount;
    }

    public BigDecimal getControlFactor() {
        return controlFactor;
    }

    public void setControlFactor(BigDecimal controlFactor) {
        this.controlFactor = controlFactor;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getSortNo() {
        return sortNo;
    }

    public void setSortNo(Integer sortNo) {
        this.sortNo = sortNo;
    }

    public LocalDateTime getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(LocalDateTime gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public LocalDateTime getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(LocalDateTime gmtModified) {
        this.gmtModified = gmtModified;
    }

    @Override
    public String toString() {
        return "DevStockTriggerConfigDO{" +
                "id=" + id +
                ", configType=" + configType +
                ", memberName='" + memberName + '\'' +
                ", triggerCount=" + triggerCount +
                ", controlFactor=" + controlFactor +
                ", remark='" + remark + '\'' +
                ", sortNo=" + sortNo +
                ", gmtCreate=" + gmtCreate +
                ", gmtModified=" + gmtModified +
                '}';
    }
}