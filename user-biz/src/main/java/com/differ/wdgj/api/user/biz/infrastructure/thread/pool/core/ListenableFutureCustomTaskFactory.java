package com.differ.wdgj.api.user.biz.infrastructure.thread.pool.core;

import java.util.concurrent.Callable;

/**
 * @Description ListenableFutureCustomTask工厂
 * <AUTHOR>
 * @Date 2023/12/26 13:33
 */
public class ListenableFutureCustomTaskFactory {

    /**
     * 创建可监听的Future
     * @param task
     * @return
     */
    public static ListenableFutureCustomTask create(Runnable task) {
        if (task instanceof MemberRunnable) {
            return new ListenableFutureCustomMemberTask((MemberRunnable) task);
        }
        return new ListenableFutureCustomTask(task);
    }

    /**
     * 创建可监听的Future
     * @param task
     * @param <T>
     * @return
     */
    public static <T> ListenableFutureCustomTask<T> create(Callable<T> task) {
        if (task instanceof MemberCallable) {
            return new ListenableFutureCustomMemberTask((MemberCallable) task);
        }
        return new ListenableFutureCustomTask(task);
    }
}
