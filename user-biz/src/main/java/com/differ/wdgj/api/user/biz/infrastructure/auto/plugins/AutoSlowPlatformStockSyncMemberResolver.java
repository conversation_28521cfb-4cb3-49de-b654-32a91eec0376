package com.differ.wdgj.api.user.biz.infrastructure.auto.plugins;

import com.differ.wdgj.api.user.biz.infrastructure.auto.AbstractAutoJobMemberResolver;

/**
 * 慢平台自动库存同步任务会员解析器
 * <p>
 * 库存同步接口非常慢的平台，会影响到其他正常平台的库存同步速率
 *
 * <AUTHOR>
 * @date 2025/1/15 10:00
 */
public class AutoSlowPlatformStockSyncMemberResolver extends AbstractAutoJobMemberResolver {
    
    // region 重写基类方法

    /**
     * 是否开启慢平台自动库存同步
     *
     * @param memberName 会员名
     * @return 结果
     */
    @Override
    public boolean enableAuto(String memberName) {
        // TODO: 后续根据业务配置实现具体的判断逻辑
        // 判断会员是否有慢平台需要库存同步
        return true;
    }

    // endregion
}