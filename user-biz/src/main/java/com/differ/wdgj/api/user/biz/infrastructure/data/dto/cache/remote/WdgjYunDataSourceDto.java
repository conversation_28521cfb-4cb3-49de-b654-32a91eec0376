package com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.remote;

/**
 * 网店管家云端 - 数据库链接缓存实体
 *
 * <AUTHOR>
 * @date 2024-03-26 14:34
 */
public class WdgjYunDataSourceDto {

    /**
     * 数据库连接
     */
    private String address;

    /**
     * 集群Id
     */
    private String groupId;

    /**
     * 备注
     */
    private String memo;

    /**
     * 数据库连接用户名
     */
    private String userName;

    /**
     * 数据库连接密码
     */
    private String password;

    /**
     * 数据库连接端口号
     */
    private Integer port;

    /**
     * 数据库连接Id
     */
    private String rdsId;

    /**
     * 数据库连接类别
     */
    private String rdsType;

    /**
     *
     */
    private String roPassword;

    /**
     *
     */
    private String roUserName;

    //region get/set
    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public Integer getPort() {
        return port;
    }

    public void setPort(Integer port) {
        this.port = port;
    }

    public String getRdsId() {
        return rdsId;
    }

    public void setRdsId(String rdsId) {
        this.rdsId = rdsId;
    }

    public String getRdsType() {
        return rdsType;
    }

    public void setRdsType(String rdsType) {
        this.rdsType = rdsType;
    }

    public String getRoPassword() {
        return roPassword;
    }

    public void setRoPassword(String roPassword) {
        this.roPassword = roPassword;
    }

    public String getRoUserName() {
        return roUserName;
    }

    public void setRoUserName(String roUserName) {
        this.roUserName = roUserName;
    }
    //endregion
}
