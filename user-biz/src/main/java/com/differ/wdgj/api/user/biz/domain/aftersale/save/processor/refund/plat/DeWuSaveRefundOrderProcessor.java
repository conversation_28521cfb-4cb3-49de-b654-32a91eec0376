package com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.refund.plat;

import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.AfterSaleSaveContext;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.handle.AfterSaleHandleResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.SourceAfterSaleOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.target.TargetCovertOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.refund.BaseSaveRefundOrderProcessor;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetRefundOrderResponseOrderItem;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiReturnListDO;

/**
 * 得物 - 保存退货退款单处理器
 *
 * <AUTHOR>
 * @date 2024/7/23 下午2:33
 */
public class DeWuSaveRefundOrderProcessor extends BaseSaveRefundOrderProcessor {
    //region 构造

    /**
     * 构造
     *
     * @param context 上下文
     */
    public DeWuSaveRefundOrderProcessor(AfterSaleSaveContext context) {
        super(context);
    }
    //endregion

    /**
     * 转换订单级信息
     *
     * @param sourceOrder 原始售后单数据
     * @param targetOrder 目标售后单数据
     * @return 结果
     */
    @Override
    protected AfterSaleHandleResult orderConvert(SourceAfterSaleOrderItem<BusinessGetRefundOrderResponseOrderItem> sourceOrder, TargetCovertOrderItem targetOrder) {
        // 基础数据
        BusinessGetRefundOrderResponseOrderItem ployOrder = sourceOrder.getPloyOrder();
        ApiReturnListDO afterSaleOrder = targetOrder.getAfterSaleOrder();

        // 保存业务类型
        afterSaleOrder.setOrderType(ployOrder.getShopType());

        return AfterSaleHandleResult.success();
    }
}
