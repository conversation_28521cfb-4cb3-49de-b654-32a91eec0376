package com.differ.wdgj.api.user.biz.infrastructure.dbsafe.query;

// region 构造方法

import com.differ.wdgj.api.user.biz.infrastructure.common.CpuControl;
import com.differ.wdgj.api.user.biz.infrastructure.dbsafe.config.DbSafePageQueryConfig;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.concurrent.atomic.AtomicLong;

public class SafeDbQueryOperation {

    // region 构造方法

    /**
     * 私有构造
     */
    private SafeDbQueryOperation() {
    }

    // endregion

    // region 公共方法

    /**
     * 构建实例
     *
     * @return 结果
     */
    public static SafeDbQueryOperation build() {
        return new SafeDbQueryOperation();
    }

    /**
     * 增量分页查询
     *
     * @param queryConfig 分页查询配置
     * @param queryFunc   分页查询方法
     * @param <T>         泛型
     * @return 查询出的总数
     */
    public <T> int incrementPageQuery(DbSafePageQueryConfig queryConfig, IDbSafeIncrementPageQuery<T> queryFunc) {

        // 已经查询出的总数
        int hasQueryTotal = 0;

        // 增量 ID
        AtomicLong incrementId = new AtomicLong();

        // 分页大小
        int pageSize = queryConfig.getPageSize();

        // 最大页码
        int maxPageIndex = queryConfig.getMaxPageIndex();

        // 当前页码
        int pageIndex = 0;

        // 循环执行
        while (pageIndex < maxPageIndex) {

            // 增量分页查询
            List<T> queryRet = queryFunc.pageQuery(pageSize, incrementId.get());
            if (CollectionUtils.isEmpty(queryRet)) {
                break;
            }

            // 已经查询出的总数累加
            hasQueryTotal += queryRet.size();

            // 执行业务
            boolean continueExec = queryFunc.execBiz(queryRet);
            if (!continueExec) {
                break;
            }

            // 末尾元素
            T lastItem = queryRet.get(queryRet.size() - 1);

            // 获取增量 ID
            Long lastId = queryFunc.getIncrementId(lastItem);

            // 更新增量 ID
            incrementId.set(lastId);

            // 查询结果数量 < 分页大小：终止循环
            if (queryRet.size() < pageSize) {
                break;
            }

            // 累加当前页码
            pageIndex++;

            // CPU 控制
            CpuControl.singleton().checkLoop();
        }

        return hasQueryTotal;
    }

    // endregion
}
