package com.differ.wdgj.api.user.biz.domain.stock.data.auto;

import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.PolyPlatEnum;
import org.apache.commons.collections.MapUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 库存降频配置
 *
 * <AUTHOR>
 * @date 2025/8/7 15:18
 */
public class StockDowngradeFrequencyConfig {

    /**
     * 构造方法
     */
    public StockDowngradeFrequencyConfig() {
        this.downgradeFactors = new HashMap<>();
    }

    /**
     * 降频系数
     */
    private Map<PolyPlatEnum, String> downgradeFactors;

    /**
     * 添加降频系数
     *
     * @param plat   平台
     * @param factor 降频系数
     */
    public void addFactor(PolyPlatEnum plat, String factor) {
        this.downgradeFactors.put(plat, factor);
    }

    /**
     * 获取降频系数
     *
     * @param plat 平台
     * @return 降频系数
     */
    public String getFactor(PolyPlatEnum plat) {
        return this.downgradeFactors.get(plat);
    }

    /**
     * 是否为空
     *
     * @return 是否为空
     */
    public boolean isEmpty() {
        return MapUtils.isEmpty(this.downgradeFactors);
    }

    // region getter & setter

    public Map<PolyPlatEnum, String> getDowngradeFactors() {
        return downgradeFactors;
    }

    public void setDowngradeFactors(Map<PolyPlatEnum, String> downgradeFactors) {
        this.downgradeFactors = downgradeFactors;
    }

    // endregion

}
