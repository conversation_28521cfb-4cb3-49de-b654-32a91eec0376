package com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.exchange.filter;

import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.AfterSaleSaveContext;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.handle.AfterSaleHandleResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.SourceAfterSaleOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.target.TargetCovertOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.handle.core.AbstractPreFiltrationOrderHandle;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetExchangeOrderResponseOrderItem;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;

/**
 * <AUTHOR>
 * @date 2025/6/26 10:01
 */
public class ExchangePlatUpdateHisFilterHandle extends AbstractPreFiltrationOrderHandle<BusinessGetExchangeOrderResponseOrderItem> {
    //region 构造
    public ExchangePlatUpdateHisFilterHandle(AfterSaleSaveContext context) {
        super(context);
    }
    //endregion

    //region 实现基类方法

    /**
     * 前置过滤
     *
     * @param orderItem 原始售后单列表
     * @return 过滤结果
     */
    @Override
    public AfterSaleHandleResult preFiltrationOrder(SourceAfterSaleOrderItem<BusinessGetExchangeOrderResponseOrderItem> orderItem, TargetCovertOrderItem targetOrder) {
        BusinessGetExchangeOrderResponseOrderItem polyExchangeOrder = orderItem.getPloyOrder();
        if (polyExchangeOrder == null) {
            return AfterSaleHandleResult.success();
        }

        //region 买家删除售后单时，售后单更新时间会变更，超过指定月数就不保存

        LocalDateTime createTime = polyExchangeOrder.getCreateTime();
        LocalDateTime updateTime = polyExchangeOrder.getUpdateTime();
        //更新时间 - 创建时间 不能超过 maxUpdateMonth 个月
        if (createTime != null) {
            //单位：月数。
            int maxUpdateMonth = 6;

            //更新时间为空时，默认赋值当前时间
            if (updateTime == null || updateTime.equals(LocalDateTime.MIN) || updateTime.equals(LocalDateTime.MAX)) {
                updateTime = LocalDateTime.now();
            }

            //计算时间差
            int days = (int) createTime.until(updateTime, ChronoUnit.MONTHS);
            if (days >= maxUpdateMonth) {
                //更新时间 - 创建时间 >= maxUpdateMonth 个月，售后单不保存
                return AfterSaleHandleResult.failed(String.format("超过%s个月的售后单不保存，创建时间=%s，更新时间=%s", maxUpdateMonth, createTime, updateTime));
            }
        }

        //endregion

        return AfterSaleHandleResult.success();
    }

    /**
     * 标题
     *
     * @return 标题
     */
    @Override
    public String caption() {
        return "平台更新历史换货单过滤器";
    }
    //endregion
}
