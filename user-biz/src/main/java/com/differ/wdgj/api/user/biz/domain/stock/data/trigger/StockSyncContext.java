package com.differ.wdgj.api.user.biz.domain.stock.data.trigger;

import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.PolyPlatEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.enums.StockSyncTriggerTypeEnum;
import com.differ.wdgj.api.user.biz.domain.stock.multi.adapter.core.IMultiWarehouseAdapter;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.bizfeature.plugins.SyncStockContent;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.local.ApiShopBaseDto;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.shop.config.SyncStockShopConfig;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.shop.ShopTypeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.utils.plat.SyncStockBizFeatureUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 库存同步上下文
 *
 * <AUTHOR>
 * @date 2024-02-26 14:09
 */
public class StockSyncContext {

    /**
     * 会员名
     */
    private String vipUser;

    /**
     * 店铺Id
     */
    private Integer shopId;

    /**
     * 平台
     */
    private PolyPlatEnum plat;

    /**
     * 慢速标识
     */
    private Boolean slowSpeedSign;

    /**
     * 店铺基础信息
     */
    private ApiShopBaseDto shopBase;

    /**
     * 库存同步配置
     */
    private SyncStockShopConfig syncStockConfig;

    /**
     * 多仓业务适配器
     */
    private IMultiWarehouseAdapter multiWhsAdapter;

    /**
     * 触发类型
     */
    private StockSyncTriggerTypeEnum triggerType;

    /**
     * 操作人
     */
    private String operatorName;

    /**
     * 上下文 ID
     */
    private Integer contextId;

    /**
     * 库存同步平台业务特性
     */
    private final Map<String, SyncStockContent> syncStockFeatureMap = new HashMap<>();

    //region 公共方法
    /**
     * 获取库存同步平台业务特性
     *
     * @return 售后平台业务特性
     */
    public SyncStockContent getSyncStocksPlatFeature(){
        return getSyncStocksPlatFeature(StringUtils.EMPTY);
    }

    /**
     * 获取库存同步平台业务特性
     *
     * @param restraintKey 业务配置键
     * @return 售后平台业务特性
     */
    public SyncStockContent getSyncStocksPlatFeature(String restraintKey) {
        if (shopBase == null) {
            return null;
        }
        // default 是由于platFeatureMap的key不能为空字符串
        if(StringUtils.isEmpty(restraintKey)){
            restraintKey = ShopTypeEnum.DEFAULT.getCode();
        }
        return syncStockFeatureMap.computeIfAbsent(restraintKey, key -> {
            // 反向替换default
            String keyNoDefault = ShopTypeEnum.DEFAULT.getCode().equalsIgnoreCase(key)
                    ? StringUtils.EMPTY
                    : key;
            return SyncStockBizFeatureUtils.singleton().getFeature(shopBase, keyNoDefault);
        });
    }
    //endregion

    //region get/set

    public String getVipUser() {
        return vipUser;
    }

    public void setVipUser(String vipUser) {
        this.vipUser = vipUser;
    }

    public Integer getShopId() {
        return shopId;
    }

    public void setShopId(Integer shopId) {
        this.shopId = shopId;
    }

    public PolyPlatEnum getPlat() {
        return plat;
    }

    public void setPlat(PolyPlatEnum plat) {
        this.plat = plat;
    }

    public ApiShopBaseDto getShopBase() {
        return shopBase;
    }

    public void setShopBase(ApiShopBaseDto shopBase) {
        this.shopBase = shopBase;
    }
    public StockSyncTriggerTypeEnum getTriggerType() {
        return triggerType;
    }

    public void setTriggerType(StockSyncTriggerTypeEnum triggerType) {
        this.triggerType = triggerType;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public Integer getContextId() {
        return contextId;
    }

    public void setContextId(Integer contextId) {
        this.contextId = contextId;
    }

    public SyncStockShopConfig getSyncStockConfig() {
        return syncStockConfig;
    }

    public void setSyncStockConfig(SyncStockShopConfig syncStockConfig) {
        this.syncStockConfig = syncStockConfig;
    }

    public IMultiWarehouseAdapter getMultiWhsAdapter() {
        return multiWhsAdapter;
    }

    public void setMultiWhsAdapter(IMultiWarehouseAdapter multiWhsAdapter) {
        this.multiWhsAdapter = multiWhsAdapter;
    }

    public Boolean getSlowSpeedSign() {
        return slowSpeedSign;
    }

    public void setSlowSpeedSign(Boolean slowSpeedSign) {
        this.slowSpeedSign = slowSpeedSign;
    }

    public Map<String, SyncStockContent> getSyncStockFeatureMap() {
        return syncStockFeatureMap;
    }

    //endregion
}
