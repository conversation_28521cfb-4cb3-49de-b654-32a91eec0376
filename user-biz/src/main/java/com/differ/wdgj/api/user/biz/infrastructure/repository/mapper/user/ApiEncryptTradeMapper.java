package com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user;

import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiEncryptTradeDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 原始密文表[g_api_encrypttradelist]
 *
 * <AUTHOR>
 * @date 2024-07-02 20:34
 */
public interface ApiEncryptTradeMapper {
    /**
     * 根据业务单号批量查询密文信息
     *
     * @param type     业务类型<see>TradeEncryptTypeEnum</see>
     * @param shopId   店铺ID
     * @param tradeNos 单号
     * @return ReturnInfo列表
     */
    List<ApiEncryptTradeDO> selectByTradeNos(@Param("type") int type, @Param("shopId") int shopId, @Param("tradeNos") List<String> tradeNos);

    /**
     * 批量新增/更新密文信息
     * <p>ON DUPLICATE KEY UPDATE 废弃，不推荐使用</p>
     *
     * @param list 售后单退货商品列表
     * @return 影响行数
     */
    @Deprecated
    Integer batchInsertOrUpdate(@Param("list") List<ApiEncryptTradeDO> list);

    /**
     * 批量新增密文信息
     *
     * @param list 售后单退货商品列表
     * @return 影响行数
     */
    Integer batchInsert(@Param("list") List<ApiEncryptTradeDO> list);

    /**
     * 批量更新密文信息
     *
     * @param list 售后单退货商品列表
     * @return 影响行数
     */
    Integer batchUpdate(@Param("list") List<ApiEncryptTradeDO> list);
}
