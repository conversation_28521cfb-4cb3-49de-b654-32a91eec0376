package com.differ.wdgj.api.user.biz.infrastructure.data.enums;

import com.alibaba.fastjson.annotation.JSONType;
import com.differ.wdgj.api.component.util.enums.*;
import org.apache.commons.lang3.StringUtils;

/**
 * 外部业务平台
 *
 * <AUTHOR>
 * @Date 2020/2/18 11:28
 */
@JSONType(deserializer = EnumCodeValueDeserializer.class, serializer = EnumCodeValueWriteSerializer.class)
public enum OuterApiEnum implements ValueEnum {

    /**
     * 无
     */
    NONE(0, "无"),
    /**
     * 网店管家
     */
    WDGJ(1, "网店管家"),
    /**
     * 新门店
     */
    NEWMENDIAN(2, "新门店");

    //region 构造
    OuterApiEnum(Integer value, String description) {
        this.value = value;
        this.description = description;
    }
    //endregion

    //region 常量
    private final Integer value;

    private final String description;

    /**
     * 标题
     */
    public static final String OUT_USER_NAME_SPLIT = "■";
    //endregion

    //region 公共方法
    @Override
    public Integer getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }

    @Override
    public String toString() {
        return value.toString();
    }

    /**
     * 获取api会员名
     *
     * @param outAccount 外部会员名
     * @return api会员名
     */
    public String getApiUserName(String outAccount) {
        return String.format("%s%s%d", outAccount, OUT_USER_NAME_SPLIT, this.getValue());
    }
    /**
     * 获取外部会员名
     *
     * @param apiUserName api会员名
     * @return 外部会员名
     */
    public String getOutUserName(String apiUserName) {
        String[] strings = StringUtils.split(apiUserName, OUT_USER_NAME_SPLIT);
        if(strings.length > 0){
            return strings[0];
        }
        return StringUtils.EMPTY;
    }
    //endregion

    //region 公共静态方法
    /**
     * 根据平台值获取对应的枚举。
     *
     * @param value 值
     * @return 对应的枚举
     */
    public static OuterApiEnum create(int value) {
        return EnumConvertCacheUtil.convert(value, OuterApiEnum.class, EnumConvertType.VALUE);
    }
    //endregion
}
