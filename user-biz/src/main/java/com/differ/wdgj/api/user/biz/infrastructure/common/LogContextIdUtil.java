package com.differ.wdgj.api.user.biz.infrastructure.common;

import com.differ.jackyun.framework.component.basic.constants.HeaderKey;
import com.differ.jackyun.framework.component.basic.interceptor.CommonContextHolder;
import com.differ.jackyun.framework.component.utils.id.IdWorkerUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;

/**
 * @Description 日志上下文工具类
 * <AUTHOR>
 * @Date 2023/3/10 14:19
 */
public class LogContextIdUtil {

    /**
     * 不存在日志ID时，初始化日志上下文Id
     *
     * @return 日志上下文Id
     */
    public static Long initLogIdIfNoExists() {
        Long loggerSn = CommonContextHolder.getLoggerSn();
        String sn = MDC.get(HeaderKey.LoggerSnKey);
        if (StringUtils.isBlank(sn) || loggerSn == null || loggerSn.equals(0L)) {
            return initNewLogId();
        }
        return loggerSn;
    }

    /**
     * 初始化新的日志上下文Id
     *
     * @return 日志上下文Id
     */
    public static Long initNewLogId() {
        Long loggerSn = IdWorkerUtil.getId();
        CommonContextHolder.setLoggerSn(loggerSn);
        return loggerSn;
    }

    /**
     * 设置日志ID
     *
     * @return 日志上下文Id
     */
    public static void setLogId(Long loggerSn) {
        if (loggerSn == null || loggerSn.equals(0L)) {
            loggerSn = IdWorkerUtil.getId();
        }
        CommonContextHolder.setLoggerSn(loggerSn);
    }
}
