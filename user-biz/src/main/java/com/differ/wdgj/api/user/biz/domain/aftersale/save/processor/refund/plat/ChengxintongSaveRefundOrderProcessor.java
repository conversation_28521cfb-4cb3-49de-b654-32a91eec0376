package com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.refund.plat;

import com.differ.wdgj.api.user.biz.domain.aftersale.common.data.AfterSaleBasicInfo;
import com.differ.wdgj.api.user.biz.domain.aftersale.common.subdomain.IBasicsAfterSaleOperationService;
import com.differ.wdgj.api.user.biz.domain.aftersale.common.subdomain.impl.BasicsAfterSaleOperationService;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.AfterSaleSaveContext;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.handle.AfterSaleHandleResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.DbAfterSaleOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.SourceAfterSaleOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.target.TargetCovertOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.refund.BaseSaveRefundOrderProcessor;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetRefundOrderResponseOrderItem;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiReturnListDO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 诚信通
 *
 * <AUTHOR>
 * @date 2025/3/4 下午5:14
 */
public class ChengxintongSaveRefundOrderProcessor extends BaseSaveRefundOrderProcessor {
    //region 常量
    /**
     * 常量
     */
    private static final String TQ_STR = "TQ";
    //endregion

    //region 构造

    /**
     * 构造
     *
     * @param context 上下文
     */
    public ChengxintongSaveRefundOrderProcessor(AfterSaleSaveContext context) {
        super(context);
    }
    //endregion

    /**
     * 前置批量查询
     *
     * @param orderItems 原始售后单数据列表
     * @return 结果
     */
    @Override
    protected AfterSaleHandleResult preBatchOrder(List<SourceAfterSaleOrderItem<BusinessGetRefundOrderResponseOrderItem>> orderItems) {
        // 空校验
        if (CollectionUtils.isEmpty(orderItems)) {
            return AfterSaleHandleResult.success();
        }

        // 诚信通兼容售后单号TQ
        AfterSaleHandleResult compatibleResult = compatibleRefundIdTq(orderItems);
        if (compatibleResult.isFailed()) {
            return compatibleResult;
        }

        return AfterSaleHandleResult.success();
    }

    /**
     * 转换订单级信息
     *
     * @param sourceOrder 原始售后单数据
     * @param targetOrder 目标售后单数据
     * @return 结果
     */
    @Override
    protected AfterSaleHandleResult orderConvert(SourceAfterSaleOrderItem<BusinessGetRefundOrderResponseOrderItem> sourceOrder, TargetCovertOrderItem targetOrder) {
        // 基础数据
        DbAfterSaleOrderItem dbOrder = sourceOrder.getDbOrder();
        ApiReturnListDO afterSaleOrder = targetOrder.getAfterSaleOrder();

        // TQ单号兼容赋值历史单号
        if (dbOrder != null && dbOrder.getAfterSaleOrder() != null) {
            afterSaleOrder.setRefundId(dbOrder.getAfterSaleOrder().getRefundId());
        }

        return AfterSaleHandleResult.success();
    }

    //region 私有方法

    /**
     * 诚信通兼容售后单号TQ
     *
     * @param orderItems 原始售后单数据列表
     * @return 结果
     */
    private AfterSaleHandleResult compatibleRefundIdTq(List<SourceAfterSaleOrderItem<BusinessGetRefundOrderResponseOrderItem>> orderItems) {
        // 获取售后单Id
        List<String> refundIdsByTQ = new ArrayList<>();
        List<String> refundIdsByNoTQ = new ArrayList<>();
        orderItems.forEach(x -> {
            String afterSaleNo = x.getAfterSaleNo();
            if (StringUtils.isNotEmpty(afterSaleNo)) {
                if (afterSaleNo.startsWith(TQ_STR)) {
                    refundIdsByNoTQ.add(afterSaleNo.substring(TQ_STR.length()));
                } else {
                    refundIdsByTQ.add(TQ_STR + afterSaleNo);
                }
            }
        });

        // 基础数据
        String memberName = context.getMemberName();
        Integer shopId = context.getShopId();
        IBasicsAfterSaleOperationService afterSaleOperation = new BasicsAfterSaleOperationService();

        // 查询有TQ售后单号数据
        List<AfterSaleBasicInfo> afterSaleBasicInfosByTQ = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(refundIdsByTQ)) {
            afterSaleBasicInfosByTQ = afterSaleOperation.queryAfterSale(memberName, shopId, refundIdsByTQ);
        }
        // 查询无TQ售后单号数据
        List<AfterSaleBasicInfo> afterSaleBasicInfosByNoTQ = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(refundIdsByNoTQ)) {
            afterSaleBasicInfosByNoTQ = afterSaleOperation.queryAfterSale(memberName, shopId, refundIdsByNoTQ);
        }

        // 重新构建历史售后单信息
        for (SourceAfterSaleOrderItem<BusinessGetRefundOrderResponseOrderItem> orderItem : orderItems) {
            // 仅历史售后单为空时重新构建
            if (orderItem.getDbOrder() == null || orderItem.getDbOrder().getAfterSaleOrder() == null) {
                // TQ售后单号处理
                AfterSaleBasicInfo afterSaleBasicInfoByTQ = afterSaleBasicInfosByTQ.stream().filter(x -> compareRefundIdByTq(x.getApiReturnListDO().getRefundId(), orderItem.getAfterSaleNo())).findFirst().orElse(null);
                if (afterSaleBasicInfoByTQ != null) {
                    DbAfterSaleOrderItem dbOrderItem = buildDbAfterSaleOrder(orderItem.getDbOrder(), afterSaleBasicInfoByTQ);
                    orderItem.setDbOrder(dbOrderItem);
                }
                // 无TQ售后单号处理
                AfterSaleBasicInfo afterSaleBasicInfoByNoTQ = afterSaleBasicInfosByNoTQ.stream().filter(x -> compareRefundIdByTq(x.getApiReturnListDO().getRefundId(), orderItem.getAfterSaleNo())).findFirst().orElse(null);
                if (afterSaleBasicInfoByNoTQ != null) {
                    DbAfterSaleOrderItem dbOrderItem = buildDbAfterSaleOrder(orderItem.getDbOrder(), afterSaleBasicInfoByNoTQ);
                    orderItem.setDbOrder(dbOrderItem);
                }
            }
        }

        return AfterSaleHandleResult.success();
    }

    /**
     * 构建历史售后单信息
     *
     * @param dbOrderItem            现有历史售后单信息
     * @param afterSaleBasicInfoByTQ 新售后单信息
     * @return 历史售后单信息
     */
    private DbAfterSaleOrderItem buildDbAfterSaleOrder(DbAfterSaleOrderItem dbOrderItem, AfterSaleBasicInfo afterSaleBasicInfoByTQ) {
        if (afterSaleBasicInfoByTQ == null) {
            return dbOrderItem;
        }
        if (dbOrderItem == null) {
            dbOrderItem = new DbAfterSaleOrderItem();
        }
        // 构建历史售后单数据
        dbOrderItem.setAfterSaleOrder(afterSaleBasicInfoByTQ.getApiReturnListDO());
        dbOrderItem.setReturnGoods(afterSaleBasicInfoByTQ.getReturnGoods());
        dbOrderItem.setExchangeGoods(afterSaleBasicInfoByTQ.getExchangeGoods());
        dbOrderItem.setAfterSaleTableType(afterSaleBasicInfoByTQ.getAfterSaleTableType());

        return dbOrderItem;
    }

    /**
     * Tq兼容比较售后单号
     *
     * @param nowRefundId 当前售后单号
     * @param newRefundId 新售后单号
     * @return 结果
     */
    private boolean compareRefundIdByTq(String nowRefundId, String newRefundId) {
        String nowRefundIdNoTq = nowRefundId.startsWith(TQ_STR)
                ? nowRefundId.replace(TQ_STR, StringUtils.EMPTY)
                : nowRefundId;
        String newRefundIdNoTq = newRefundId.startsWith(TQ_STR)
                ? newRefundId.replace(TQ_STR, StringUtils.EMPTY)
                : newRefundId;
        return nowRefundIdNoTq.equalsIgnoreCase(newRefundIdNoTq);
    }

    //endregion
}
