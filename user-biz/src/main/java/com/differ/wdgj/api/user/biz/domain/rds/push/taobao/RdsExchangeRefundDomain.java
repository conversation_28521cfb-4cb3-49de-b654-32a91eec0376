package com.differ.wdgj.api.user.biz.domain.rds.push.taobao;

import com.differ.wdgj.api.component.util.json.JsonUtils;
import com.differ.wdgj.api.component.util.spring.BeanContextUtil;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetExchangeOrderResponseOrderItem;
import com.differ.wdgj.api.user.biz.domain.rds.push.taobao.convertor.RdsExchangeRefundConvertor;
import com.differ.wdgj.api.user.biz.domain.rds.push.taobao.data.exchage.ExchangeRefundJdpResponseDto;
import com.differ.wdgj.api.user.biz.domain.rds.push.taobao.data.refund.RdsTbRefundOutRequest;
import com.differ.wdgj.api.user.biz.domain.rds.push.taobao.data.refund.TbAfterSaleRdsPageQueryResponse;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.rdspush.ExchangeRefundRdsDo;
import com.differ.wdgj.api.user.biz.infrastructure.repository.core.DBSwitchUtil;
import com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user.ExchangeRefundRdsMapper;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 淘宝Rds - 换货相关操作（jdp_exchange_refund）
 *
 * <AUTHOR>
 * @date 2025/4/10 下午9:14
 */
public class RdsExchangeRefundDomain {
    //region 常量
    /**
     * 会员名
     */
    private final String memberName;
    //endregion

    //region 构造
    public RdsExchangeRefundDomain(String memberName) {
        this.memberName = memberName;
    }
    //endregion

    /**
     * 查询淘宝退货退款单总数
     *
     * @param request 请求参数
     * @return 淘宝退货退款单总数
     */
    public int queryPolyRefundOrderCount(RdsTbRefundOutRequest request){
        // 查询推送库售后单列表
        if (request == null) {
            return 0;
        }

        ExchangeRefundRdsMapper exchangeRefundRdsMapper = BeanContextUtil.getBean(ExchangeRefundRdsMapper.class);
        return DBSwitchUtil.doDBWithRds(memberName, () -> exchangeRefundRdsMapper.getExchangeRefundsCountByModified(request.getStartUpdateTime(), request.getEndUpdateTime(), request.getSellNick()));
    }

    /**
     * 查询淘宝退货退款单（转换为菠萝派对象）
     *
     * @param request 请求参数
     * @return 淘宝退货退款单列表
     */
    public TbAfterSaleRdsPageQueryResponse queryPolyRefundOrder(RdsTbRefundOutRequest request) {
        // 基础返回
        TbAfterSaleRdsPageQueryResponse response = new TbAfterSaleRdsPageQueryResponse();
        // 查询推送库售后单列表
        if (request == null) {
            return response;
        }

        // 批量获取推送库数据
        ExchangeRefundRdsMapper exchangeRefundRdsMapper = BeanContextUtil.getBean(ExchangeRefundRdsMapper.class);
        int pageSize = request.getPageSize();
        int start = (request.getPageIndex() - 1) * pageSize;
        List<ExchangeRefundRdsDo> tbExchangeRdsDos = DBSwitchUtil.doDBWithRds(memberName, () -> exchangeRefundRdsMapper.getExchangeRefundDataByModified(request.getStartUpdateTime(), request.getEndUpdateTime(), request.getSellNick(), start, pageSize));

        // 推送库售后单转换
        List<BusinessGetExchangeOrderResponseOrderItem> polyExchangeOrders = new ArrayList<>();
        for (ExchangeRefundRdsDo tbExchangeRdsDo : tbExchangeRdsDos) {
            if (StringUtils.isNotEmpty(tbExchangeRdsDo.getJdpResponse())) {
                ExchangeRefundJdpResponseDto tbRefundOrder = JsonUtils.deJson(tbExchangeRdsDo.getJdpResponse(), ExchangeRefundJdpResponseDto.class);
                BusinessGetExchangeOrderResponseOrderItem polyRefundOrder = RdsExchangeRefundConvertor.convertPolyExchangeOrder(tbRefundOrder);
                polyRefundOrder.setRdsCreateTime(tbExchangeRdsDo.getJdpCreated());
                polyRefundOrder.setRdsModifyTime(tbExchangeRdsDo.getJdpModified());
                polyExchangeOrders.add(polyRefundOrder);
            }
        }

        response.setExchanges(polyExchangeOrders);
        return response;
    }
}
