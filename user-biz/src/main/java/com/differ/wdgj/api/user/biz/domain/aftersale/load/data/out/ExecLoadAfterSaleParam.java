package com.differ.wdgj.api.user.biz.domain.aftersale.load.data.out;

import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.work.AfterSaleLoadArgs;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.enums.TriggerTypeEnum;

/**
 * 执行售后单任务入参
 *
 * <AUTHOR>
 * @date 2024/9/13 下午4:58
 */
public class ExecLoadAfterSaleParam {
    /**
     * 任务Id
     */
    private String taskId;

    /**
     * 会员
     */
    private String member;

    /**
     * 店铺ID
     */
    private Integer shopId;

    /**
     * 触发类型
     */
    private TriggerTypeEnum triggerType;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 下载基础参数
     */
    private AfterSaleLoadArgs loadArgs;


    //region get/set
    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getMember() {
        return member;
    }

    public void setMember(String member) {
        this.member = member;
    }

    public Integer getShopId() {
        return shopId;
    }

    public void setShopId(Integer shopId) {
        this.shopId = shopId;
    }

    public TriggerTypeEnum getTriggerType() {
        return triggerType;
    }

    public void setTriggerType(TriggerTypeEnum triggerType) {
        this.triggerType = triggerType;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public AfterSaleLoadArgs getLoadArgs() {
        return loadArgs;
    }

    public void setLoadArgs(AfterSaleLoadArgs loadArgs) {
        this.loadArgs = loadArgs;
    }
    //endregion
}
