package com.differ.wdgj.api.user.biz.tasks.mq.multi.core.callback.fail;

import com.differ.jackyun.framework.component.jmq.core.consumer.retry.ConsumerFailCallback;
import com.differ.wdgj.api.user.biz.tasks.mq.multi.core.data.QueueHeader;

/**
 * 多队列失败回调：重试最大次数失败时回调
 *
 * <AUTHOR>
 * @date 2024/4/3 14:38
 */
public interface MultiConsumerFailCallback<T> extends ConsumerFailCallback<String> {

    /**
     * 多队列失败回调
     * @param message
     * @param header
     */
    void multiFailCallback(T message, QueueHeader header);
}
