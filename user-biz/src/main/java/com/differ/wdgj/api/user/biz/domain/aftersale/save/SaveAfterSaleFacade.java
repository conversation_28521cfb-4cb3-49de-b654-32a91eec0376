package com.differ.wdgj.api.user.biz.domain.aftersale.save;

import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.AfterSaleSaveContext;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.out.SaveAfterSaleOrderResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.out.SaveAfterSaleOutResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.out.SaveExchangeOrderParam;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.out.SaveRefundOrderParam;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.SaveAfterSaleResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.order.SaveOrderResultComposite;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.operation.AfterSaleSaveContextOperation;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.ISaveAfterSaleOrder;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.exchange.SaveExchangeOrderFactory;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.refund.SaveRefundOrderFactory;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.utils.AfterSaleConfigKeyUtils;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetExchangeOrderResponseOrderItem;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetRefundOrderResponseOrderItem;
import com.differ.wdgj.api.user.biz.infrastructure.common.LogFactory;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;

import java.util.ArrayList;
import java.util.IdentityHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 保存售后单-外部调用
 *
 * <AUTHOR>
 * @date 2024-06-07 17:31
 */
public class SaveAfterSaleFacade {
    //region 变量

    /**
     * 标题
     */
    private final String caption = "保存售后单";

    /**
     * 日志
     */
    private final Logger log = LogFactory.get(caption);
    // endregion

    //region 保存退货退款/仅退款

    /**
     * 保存退货退款/仅退款</p>
     * 对应菠萝派Differ.JH.Business.GetRefund节点refunds
     *
     * @param param 入参
     * @return 保存结果
     */
    public SaveAfterSaleOutResult saveRefundOrder(SaveRefundOrderParam param) {
        List<String> afterSaleNos = param.getRefundOrders().stream().map(x -> x.getRefundNo()).collect(Collectors.toList());
        try {
            // 构造全局上下文
            AfterSaleSaveContext context = new AfterSaleSaveContext();
            SaveAfterSaleOutResult covertContextResult = new AfterSaleSaveContextOperation().covert(param, context);
            if (covertContextResult != null && covertContextResult.isFailed()) {
                return covertContextResult;
            }

            // 保存退货退款/仅退款单
            ISaveAfterSaleOrder<BusinessGetRefundOrderResponseOrderItem> processor = SaveRefundOrderFactory.createProcessor(context);
            SaveAfterSaleResult<List<SaveOrderResultComposite>> saveResult = processor.saveOrder(param.getRefundOrders());

            // 构建外部保存结果
            return buildOutSaveResult(context, afterSaleNos, saveResult);
        } catch (Exception e) {
            String errorMessage = String.format("保存退货退款单失败, 售后单号列表: %s, 失败原因: %s", StringUtils.join(afterSaleNos, ","), e.getMessage());
            log.error(errorMessage, e);
            return SaveAfterSaleOutResult.failed(errorMessage);
        }
    }
    //endregion

    //region 保存换货单

    /**
     * 保存换货单
     *
     * @param param 入参
     * @return 保存结果
     */
    public SaveAfterSaleOutResult saveExchangeOrder(SaveExchangeOrderParam param) {
        List<String> afterSaleNos = param.getExchangeOrders().stream().map(x -> x.getExchangeOrderNo()).collect(Collectors.toList());
        try {
            // 构造全局上下文
            AfterSaleSaveContext context = new AfterSaleSaveContext();
            SaveAfterSaleOutResult covertContextResult = new AfterSaleSaveContextOperation().covert(param, context);
            if (covertContextResult != null && covertContextResult.isFailed()) {
                return covertContextResult;
            }

            // 保存退货退款/仅退款单
            ISaveAfterSaleOrder<BusinessGetExchangeOrderResponseOrderItem> processor = SaveExchangeOrderFactory.createProcessor(context);
            SaveAfterSaleResult<List<SaveOrderResultComposite>> saveResult = processor.saveOrder(param.getExchangeOrders());

            // 构建外部保存结果
            return buildOutSaveResult(context, afterSaleNos, saveResult);
        } catch (Exception e) {
            String errorMessage = String.format("保存换货单失败, 售后单号列表: %s, 失败原因: %s", StringUtils.join(afterSaleNos, ","), e.getMessage());
            log.error(errorMessage, e);
            return SaveAfterSaleOutResult.failed(errorMessage);
        }
    }
    //endregion

    //region 私有方法

    /**
     * 构建外部保存结果
     *
     * @param context      售后保存上下文
     * @param afterSaleNos 需保存售后单号列表
     * @param saveResult   保存结果
     * @return 外部保存结果
     */
    private SaveAfterSaleOutResult buildOutSaveResult(AfterSaleSaveContext context, List<String> afterSaleNos, SaveAfterSaleResult<List<SaveOrderResultComposite>> saveResult) {
        // 整体失败，视为所有订单失败
        if (saveResult == null) {
            return SaveAfterSaleOutResult.failed("保存售后单结果为空");
        }
        if (saveResult.isFailed()) {
            String message = saveResult.getMessage();
            // 验证是否需要重试
            List<String> needRetryAfterSaleNos = new ArrayList<>();
            boolean isNeedRetry = StringUtils.isNotEmpty(message) && AfterSaleConfigKeyUtils.isNeedRetryMessage(message);
            if (isNeedRetry) {
                needRetryAfterSaleNos.addAll(afterSaleNos);
            }
            // 构建订单级结果
            Map<String, SaveAfterSaleOrderResult> orderResultMap = afterSaleNos.stream().collect(Collectors.toMap(x -> x, x -> SaveAfterSaleOrderResult.failed(x, message, isNeedRetry)));
            // 返回失败结果
            return SaveAfterSaleOutResult.failed(message, orderResultMap, needRetryAfterSaleNos);
        }

        // 整体成功，验证订单级状态
        StringBuilder simpleLogDetails = new StringBuilder();
        List<String> needRetryAfterSaleNos = new ArrayList<>();
        Map<String, SaveAfterSaleOrderResult> orderResultMap = new IdentityHashMap<>();
        List<SaveOrderResultComposite> resultComposites = saveResult.getContent();
        if (CollectionUtils.isNotEmpty(resultComposites)) {
            for (SaveOrderResultComposite resultComposite : resultComposites) {
                // 聚合结果
                SaveAfterSaleOrderResult orderResult = resultComposite.convergedResults();
                if (orderResult != null) {
                    String afterSaleNo = orderResult.getAfterSaleNo();
                    // 是否需要重试
                    if (orderResult.isNeedRetry()) {
                        needRetryAfterSaleNos.add(afterSaleNo);
                    }
                    orderResultMap.put(afterSaleNo, orderResult);

                    // 订单级简单日志
                    String successResult = orderResult.isSuccess()
                            ? "成功"
                            : String.format("失败，原因：%s", orderResult.getMessage());
                    String retryResult = orderResult.isNeedRetry() ? "是" : "否";
                    String refundType = orderResult.getWdgjRefundType() != null
                            ? orderResult.getWdgjRefundType().getDescription()
                            : "售后单";
                    String logDetail = String.format("%s[%s]保存%s；是否需要重试：%s。", refundType, afterSaleNo, successResult, retryResult);
                    simpleLogDetails.append(logDetail);
                }
            }
        }

        // 记录简单链路日志
        if (StringUtils.isNotEmpty(simpleLogDetails)) {
            LogFactory.info(caption, context.getMemberName(), () -> simpleLogDetails);
        }

        return SaveAfterSaleOutResult.success(orderResultMap, needRetryAfterSaleNos);
    }
    //endregion
}
