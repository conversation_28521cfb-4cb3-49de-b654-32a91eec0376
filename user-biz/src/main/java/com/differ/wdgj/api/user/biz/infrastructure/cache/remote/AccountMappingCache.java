package com.differ.wdgj.api.user.biz.infrastructure.cache.remote;


import com.differ.wdgj.api.component.util.spring.BeanContextUtil;
import com.differ.wdgj.api.user.biz.infrastructure.cache.remote.core.AbstractHashEnhanceCache;
import com.differ.wdgj.api.user.biz.infrastructure.cache.remote.core.DataCacheKeyEnum;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.center.MemberAccountMappingDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.SystemErrorCodes;
import com.differ.wdgj.api.user.biz.infrastructure.exception.AppException;
import com.differ.wdgj.api.user.biz.infrastructure.repository.core.DBSwitchUtil;
import com.differ.wdgj.api.user.biz.infrastructure.repository.core.SwitchDbContext;
import com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.center.MemberAccountMappingMapper;

import java.util.List;
import java.util.Map;

/**
 * 缓存：会员映射服务器信息
 *
 * <AUTHOR>
 * @date 2020-05-14 11:40
 */
public class AccountMappingCache extends AbstractHashEnhanceCache<MemberAccountMappingDO> {

    //region 构造和枚举单例

    private AccountMappingCache() {
        super(DataCacheKeyEnum.DT_MEMBER_ACCOUNTMAPPING.getCode());
        // 设置过期时间 30min~60min
        this.minCacheKeyTimeOut = 30 * 60;
        this.maxCacheKeyTimeOut = 60 * 60;
    }

    /**
     * AlarmIntervalCache
     *
     * @return
     */
    public static AccountMappingCache singleton() {
        return SingletonEnum.SINGLETON.instance;
    }

    private enum SingletonEnum {
        /**
         * 单例
         */
        SINGLETON;

        private final AccountMappingCache instance;

        private SingletonEnum() {
            instance = new AccountMappingCache();
        }
    }

    //endregion

    /**
     * 构造器。
     */


    @Override
    protected MemberAccountMappingDO loadSource(String hashField) {
        MemberAccountMappingMapper mapper = BeanContextUtil.getBean(MemberAccountMappingMapper.class);
        if (mapper == null) {
            throw new AppException(SystemErrorCodes.SYSTEMERROR, "MemberAccountMappingMapper的bean为空");
        }
        return DBSwitchUtil.doDBWithContext(SwitchDbContext.buildEsApi(), () -> mapper.selectByUserName(hashField));
    }

    @Override
    protected Map<String, MemberAccountMappingDO> loadSource(List<String> hashFields) {
        return null;
    }

    @Override
    public Class<MemberAccountMappingDO> getValueClazz() {
        return MemberAccountMappingDO.class;
    }
}

