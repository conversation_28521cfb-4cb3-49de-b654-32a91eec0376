package com.differ.wdgj.api.user.biz.domain.stock.data.result;

import com.differ.wdgj.api.user.biz.domain.stock.data.enums.MappingSetTypeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiSysMatchDO;

/**
 * 库存同步-无效店铺结果
 *
 * <AUTHOR>
 * @date 2024-03-19 11:16
 */
public class StockSyncInvalidStoreResult {

    /**
     * 匹配数据
     */
    private ApiSysMatchDO sysMatch;

    /**
     * 门店ID(京东到家、有赞等)
     */
    private String platStoreId;

    /**
     * 创建 无效店铺结果
     * @param mappingSetType 匹配类型
     * @param platStoreId 门店ID(京东到家、有赞等)
     * @param sysMatch 匹配数据
     * @return 无效店铺结果
     */
    public static StockSyncInvalidStoreResult create(MappingSetTypeEnum mappingSetType, String platStoreId, ApiSysMatchDO sysMatch){
        StockSyncInvalidStoreResult stockSyncInvalidStoreResult = new StockSyncInvalidStoreResult();
        stockSyncInvalidStoreResult.setSysMatch(sysMatch);
        stockSyncInvalidStoreResult.setPlatStoreId(platStoreId);
        return stockSyncInvalidStoreResult;
    }

    //region get/set
    public ApiSysMatchDO getSysMatch() {
        return sysMatch;
    }

    public void setSysMatch(ApiSysMatchDO sysMatch) {
        this.sysMatch = sysMatch;
    }

    public String getPlatStoreId() {
        return platStoreId;
    }

    public void setPlatStoreId(String platStoreId) {
        this.platStoreId = platStoreId;
    }
    //endregion
}
