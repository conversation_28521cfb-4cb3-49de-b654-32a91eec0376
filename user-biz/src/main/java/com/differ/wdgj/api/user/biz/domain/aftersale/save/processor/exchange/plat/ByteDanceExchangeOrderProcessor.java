package com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.exchange.plat;

import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.AfterSaleSaveContext;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.enums.AfterSalePostStatusEnum;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.DbAfterSaleOrderExtItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.exchange.BaseSaveExchangeOrderProcessor;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetExchangeOrderResponseOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.handle.AfterSaleHandleResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.SourceAfterSaleOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.target.TargetCovertOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.utils.AfterSaleCovertUtils;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiReturnListDO;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.PolyPlatEnum;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.aftersale.PolyExchangeStatusEnum;
import org.apache.commons.lang3.StringUtils;

/**
 * 抖音 - 保存换货单处理器
 *
 * <AUTHOR>
 * @date 2024/8/12 上午9:50
 */
public class ByteDanceExchangeOrderProcessor extends BaseSaveExchangeOrderProcessor {
    //region 构造

    /**
     * 构造
     *
     * @param context 上下文
     */
    public ByteDanceExchangeOrderProcessor(AfterSaleSaveContext context) {
        super(context);
    }
    //endregion

    /**
     * 转换订单级信息
     *
     * @param sourceOrder 原始售后单数据
     * @param targetOrder 目标售后单数据
     * @return 结果
     */
    @Override
    protected AfterSaleHandleResult orderConvert(SourceAfterSaleOrderItem<BusinessGetExchangeOrderResponseOrderItem> sourceOrder, TargetCovertOrderItem targetOrder) {
        // 基础信息
        BusinessGetExchangeOrderResponseOrderItem ployOrder = sourceOrder.getPloyOrder();
        ApiReturnListDO oldExchangeOrder = sourceOrder.getDbOrder().getAfterSaleOrder();
        ApiReturnListDO newExchangeOrder = targetOrder.getAfterSaleOrder();

        // 若客户在第一次换货申请被拒绝的情况下发起第二次换货申请，需要将退换单地递交状态变为待递交
        if (oldExchangeOrder != null) {
            // 第一次换货申请被拒绝 + 发起第二次换货申请
            if (StringUtils.equals(oldExchangeOrder.getReturnStatus(), PolyExchangeStatusEnum.JH_04.getWdgjValue()) &&
                    StringUtils.equals(ployOrder.getOrderStatus(), PolyExchangeStatusEnum.JH_01.getCode())) {
                // 更新为待递交，注：云端存在逻辑拒绝时更新为2
                newExchangeOrder.setCurStatus(AfterSalePostStatusEnum.WAIT_SUBMIT.getValue());
            }
        }

        // 密文处理
        AfterSaleHandleResult desensitizationResult = processDesensitization(sourceOrder, targetOrder);
        if(desensitizationResult.isFailed()){
            return desensitizationResult;
        }

        //记录卖家收退货的地址id
        if (!StringUtils.equals(ployOrder.getSellerReceiveAddressId(), "0")) {
            newExchangeOrder.setReserved1(ployOrder.getSellerReceiveAddressId());
        }

        return AfterSaleHandleResult.success();
    }

    //region 私有方法
    /**
     * 密文处理
     * <p><a href="https://s.jkyun.biz/TDjUA3X">管家API_抖店脱敏(第二版脱敏)</a> </p>
     *
     * @param sourceOrder 原始售后单数据
     * @param targetOrder 目标售后单数据
     * @return 结果
     */
    private AfterSaleHandleResult processDesensitization(SourceAfterSaleOrderItem<BusinessGetExchangeOrderResponseOrderItem> sourceOrder, TargetCovertOrderItem targetOrder) {
        BusinessGetExchangeOrderResponseOrderItem ployOrder = sourceOrder.getPloyOrder();
        DbAfterSaleOrderExtItem dbOrderExt = sourceOrder.getDbOrderExt();
        ApiReturnListDO newExchangeOrder = targetOrder.getAfterSaleOrder();

        // 基础信息处理
        newExchangeOrder.setEncryptType(context.getPlat().getValue());
        newExchangeOrder.setCustomerId(AfterSaleCovertUtils.generateHashcodeNick(ployOrder.getBuyerName(), ployOrder.getBuyerPhone()));
        // 1、密文格式化
        String flag = context.getPlat() == PolyPlatEnum.BUSINESS_FangXinGou ? "fxg" : "lub";
        newExchangeOrder.setChgSndTo(AfterSaleCovertUtils.formatDesensitizationInfo(ployOrder.getBuyerName(), flag));
        newExchangeOrder.setChgTel(AfterSaleCovertUtils.formatDesensitizationInfo(ployOrder.getBuyerPhone(), flag));
        newExchangeOrder.setChangeAdr(AfterSaleCovertUtils.formatDesensitizationInfo(ployOrder.getBuyerAddress(), flag));
        // 2、密文表处理
        targetOrder.setEncryptTrade(AfterSaleCovertUtils.covertEncryptTrade(context, ployOrder, dbOrderExt.getApiEncryptTrade(), targetOrder.getEncryptTrade()));

        return AfterSaleHandleResult.success();
    }
    //endregion
}
