package com.differ.wdgj.api.user.biz.infrastructure.cache.remote.common;

import com.differ.wdgj.api.user.biz.infrastructure.cache.remote.core.AbstractCache;
import com.differ.wdgj.api.user.biz.infrastructure.common.LogFactory;

/**
 * 报警信息Redis缓存
 *
 * <AUTHOR>
 * @date 2025/3/20 下午2:20
 */
public class AlarmIntervalCache extends AbstractCache {
    //region 构造
    private AlarmIntervalCache() {
        try {
            this.afterPropertiesSet();
        } catch (Exception e) {
            LogFactory.error("报警信息Redis缓存", "初始化hash缓存实例失败", e);
        }
    }
    //endregion

    //region 单例

    /**
     * 枚举单例
     *
     * @return
     */
    public static AlarmIntervalCache singleton() {
        return AlarmIntervalCache.SingletonEnum.SINGLETON.instance;
    }

    private enum SingletonEnum {
        /**
         * 单例
         */
        SINGLETON;

        private final AlarmIntervalCache instance;

        private SingletonEnum() {
            instance = new AlarmIntervalCache();
        }
    }
    //endregion

    /**
     * 同步缓存
     *
     * @param cacheKey 键
     * @param value    值
     * @param timeout  过期时间
     */
    public void syncValue(String cacheKey, String value, long timeout) {
        cacher.set(cacheKey, value, timeout);
    }

    /**
     * 获取缓存值
     *
     * @param cacheKey 键
     * @return 值
     */
    public String getValue(String cacheKey){
        return cacher.get(cacheKey);
    }
}
