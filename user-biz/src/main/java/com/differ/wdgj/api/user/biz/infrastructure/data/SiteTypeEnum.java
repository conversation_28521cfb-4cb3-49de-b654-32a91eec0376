package com.differ.wdgj.api.user.biz.infrastructure.data;

import com.alibaba.fastjson.annotation.JSONType;

import com.differ.wdgj.api.component.util.enums.*;
import com.differ.wdgj.api.user.biz.infrastructure.data.constant.SiteTypeCodeConst;

/**
 * 站点类别枚举  代理库存=10；代理保存订单=11；代理发货=12
 *
 * <AUTHOR>
 * @since 2019-12-23  15:08
 */
@JSONType(deserializer = EnumCodeValueDeserializer.class, serializer = EnumCodeValueWriteSerializer.class)
public enum SiteTypeEnum implements ValueEnum {

    /**
     * java综合业务服务
     */
    WDGJ_API_BUSINESS("综合业务服务", 10, SiteTypeCodeConst.WDGJ_API_BUSINESS),

    /**
     * 表示所有站点类别
     */
    ALL("表示所有站点类别", 99, SiteTypeCodeConst.ALL),
    ;

    /**
     * 构造器**
     *
     * @param caption     标题
     * @param value       枚举值
     * @param serviceCode 服务名
     */
    SiteTypeEnum(String caption, Integer value, String serviceCode) {
        this.caption = caption;
        this.value = value;
        this.serviceCode = serviceCode;
    }

    /**
     * 标题
     */
    private String caption;
    /**
     * 枚举值
     */
    private Integer value;

    /**
     * 服务名
     */
    private String serviceCode;

    /**
     * 获取标题
     *
     * @return 标题
     */
    public String getCaption() {
        return this.caption;
    }

    /**
     * 获取 枚举值
     *
     * @return 枚举值
     */
    @Override
    public Integer getValue() {
        return this.value;
    }

    public String getServiceCode() {
        return serviceCode;
    }

    /**
     * 枚举值字符串
     *
     * @return S
     */
    @Override
    public String toString() {
        return this.getValue().toString();
    }

    /**
     * 根据值获取对应的枚举。
     *
     * @param value 值
     * @return 对应的枚举
     */
    public static SiteTypeEnum convert(String value) {
        return EnumConvertCacheUtil.convert(SiteTypeEnum.class,value, EnumConvertType.VALUE);
    }
}
