package com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source;

import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiTradeCodeDO;

/**
 * 售后单hash对象
 *
 * <AUTHOR>
 * @date 2024/7/17 下午1:57
 */
public class AfterSaleOrderHashItem {

    /**
     * 老hashCode
     */
    private ApiTradeCodeDO oldHashCode;

    /**
     * 新hashCode
     */
    private ApiTradeCodeDO newHashCode;

    //region get/set
    public ApiTradeCodeDO getOldHashCode() {
        return oldHashCode;
    }

    public void setOldHashCode(ApiTradeCodeDO oldHashCode) {
        this.oldHashCode = oldHashCode;
    }

    public ApiTradeCodeDO getNewHashCode() {
        return newHashCode;
    }

    public void setNewHashCode(ApiTradeCodeDO newHashCode) {
        this.newHashCode = newHashCode;
    }
    //endregion
}
