package com.differ.wdgj.api.user.biz.infrastructure.dbsafe.config;


/**
 * 分页查询配置
 *
 * <AUTHOR>
 * @date 2024-11-27 18:13
 */
public class DbSafePageQueryConfig {

    // region 常量

    /**
     * 默认分页大小
     */
    private final static int DEFAULT_PAGE_SIZE = 1000;

    /**
     * 默认最大页码
     */
    private final static int DEFAULT_MAX_PAGE_INDEX = 1000;

    // endregion

    /**
     * 分页大小
     */
    private int pageSize;

    /**
     * 最大页码
     */
    private int maxPageIndex;

    /**
     * 创建实例
     *
     * @param pageSize     分页大小
     * @param maxPageIndex 最大页码
     * @return 结果
     */
    public static DbSafePageQueryConfig create(int pageSize, int maxPageIndex) {
        DbSafePageQueryConfig config = new DbSafePageQueryConfig();
        config.setPageSize(pageSize <= 0 ? DEFAULT_PAGE_SIZE : pageSize);
        config.setMaxPageIndex(maxPageIndex <= 0 ? DEFAULT_MAX_PAGE_INDEX : maxPageIndex);
        return config;
    }

    /**
     * 默认配置
     *
     * @return 结果
     */
    public static DbSafePageQueryConfig defaultConfig() {
        DbSafePageQueryConfig config = new DbSafePageQueryConfig();
        config.setPageSize(DEFAULT_PAGE_SIZE);
        config.setMaxPageIndex(DEFAULT_MAX_PAGE_INDEX);
        return config;
    }

    // region getter & setter

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public int getMaxPageIndex() {
        return maxPageIndex;
    }

    public void setMaxPageIndex(int maxPageIndex) {
        this.maxPageIndex = maxPageIndex;
    }

    // endregion
}