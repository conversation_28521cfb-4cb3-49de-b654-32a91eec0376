package com.differ.wdgj.api.user.biz.infrastructure.initializer.log;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.turbo.TurboFilter;
import ch.qos.logback.core.spi.FilterReply;
import com.differ.jackyun.framework.component.basic.interceptor.CommonContextHolder;
import com.differ.jackyun.framework.component.utils.address.runner.AddressInfoUpdateTaskRunner;
import com.differ.jackyun.framework.component.utils.ipaddr.JackYunIpUtils;
import com.differ.wdgj.api.user.biz.infrastructure.common.LogContextIdUtil;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Marker;

import java.util.List;

/**
 * @Description sl4f日志初始化
 * <AUTHOR>
 * @date  2022/6/1 17:08
 */
public class CustomLogInitializer extends TurboFilter {

    private LogCustomFilter logCustomFilter;

    /**
     * 初始要排除处理的类，防止死循环
     */
    private List<String> exceptLoggers = Lists.newArrayList(
            com.differ.jackyun.framework.component.basic.util.IdWorkerUtil.class.getName(),
            com.differ.jackyun.framework.component.utils.id.IdWorkerUtil.class.getName(),
            CommonContextHolder.class.getName(),
            LogCustomFilterImpl.class.getName(),
            JackYunIpUtils.class.getName());


    @Override
    public FilterReply decide(Marker marker, ch.qos.logback.classic.Logger logger, Level level, String format, Object[] params, Throwable t) {

        if(level == Level.DEBUG || level == Level.TRACE){
            return FilterReply.NEUTRAL;
        }

        if (exceptLoggers == null || exceptLoggers.contains(logger.getName())) {
            // 排除initContextId中写日志的类，防止死循环
            return FilterReply.NEUTRAL;
        }

        if (logger == null || level == null || StringUtils.isEmpty(format)) {
            return FilterReply.NEUTRAL;
        }

        FilterReply filterReply = FilterReply.NEUTRAL;
        // 处理日志自定义过滤
        if (logCustomFilter != null) {
            filterReply = logCustomFilter.filter(marker, logger, level, format, params, t);
        }
        if(filterReply == FilterReply.ACCEPT) {
            // 初始化日志上下文Id
            LogContextIdUtil.initLogIdIfNoExists();
        }

        return filterReply;
    }

    public void init(){
        LogCustomFilter filter = new LogCustomFilterImpl();
        filter.init();
        this.logCustomFilter = filter;
    }
}
