package com.differ.wdgj.api.user.biz.domain.stock.data.trigger;

import com.differ.wdgj.api.user.biz.domain.stock.data.enums.StockDetailCountTypeEnum;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 商品库存信息
 *
 * <AUTHOR>
 * @date 2025/6/20 17:27
 */
public class GoodsStockInfoDto {
    /**
     * 库存数量
     */
    private BigDecimal stockCount;

    /**
     * 库存详情
     */
    private Map<StockDetailCountTypeEnum, BigDecimal> stockDetailMap;

    //region get/set
    public BigDecimal getStockCount() {
        return stockCount;
    }

    public void setStockCount(BigDecimal stockCount) {
        this.stockCount = stockCount;
    }

    public Map<StockDetailCountTypeEnum, BigDecimal> getStockDetailMap() {
        return stockDetailMap;
    }

    public void setStockDetailMap(Map<StockDetailCountTypeEnum, BigDecimal> stockDetailMap) {
        this.stockDetailMap = stockDetailMap;
    }
    //endregion
}
