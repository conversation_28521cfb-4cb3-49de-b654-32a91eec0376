package com.differ.wdgj.api.user.biz.infrastructure.condition;

import com.differ.wdgj.api.user.biz.infrastructure.data.SiteTypeEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Condition;
import org.springframework.context.annotation.ConditionContext;
import org.springframework.core.type.AnnotatedTypeMetadata;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 根据站点是否启用bean的条件类
 *
 * <AUTHOR> chuff
 * @date 2021-09-18 14:01
 */
public class OnSiteCondition implements Condition {

    /**
     * 日志
     */
    private static final Logger log = LoggerFactory.getLogger(OnSiteCondition.class);

    /**
     * 是否监听队列
     *
     * @param context  上下文
     * @param metadata 元数据
     * @return 是否监听
     */
    @Override
    public boolean matches(ConditionContext context, AnnotatedTypeMetadata metadata) {

        try {
            Map<String, Object> attributes = metadata.getAnnotationAttributes(ConditionalOnSite.class.getName());
            if (attributes == null || attributes.size() == 0) {
                return false;
            }

            // 解析配置的监听站点
            Object sitesToMatch = attributes.get("sites");
            if (sitesToMatch == null) {
                return false;
            }

            List<String> sitesToMatchList = Arrays.asList((String[]) sitesToMatch);
            if (CollectionUtils.isEmpty(sitesToMatchList)) {
                return false;
            }

            // 解析当前站点
            String currentSite = context.getEnvironment().getProperty("system.siteType");
            if (StringUtils.isBlank(currentSite)) {
                return false;
            }

            SiteTypeEnum serviceCode = SiteTypeEnum.convert(currentSite);
            return serviceCode != null && sitesToMatchList.contains(serviceCode.getServiceCode());
        } catch (Exception ex) {
            log.error("根据站点是否启用bean解析异常", ex);
            return false;
        }
    }
}
