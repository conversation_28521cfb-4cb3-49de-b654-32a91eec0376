package com.differ.wdgj.api.user.biz.tasks.mq.multi.core;

import com.differ.wdgj.api.component.util.functional.zero.Action;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 请求触发的队列管理中心,一般用于运维后台手动操作来触发消息拉取处理
 *
 * <AUTHOR>
 * @date 2024/3/14 14:30
 */
public class HandTriggerRegister {
    /**
     * 所有任务的延迟队列Map
     */
    Map<String, Action> delegate = new ConcurrentHashMap<>();

    //region 构造和枚举单例

    private HandTriggerRegister() {
        // 私有，为了单例
    }

    /**
     * 枚举单例
     *
     * @return
     */
    public static HandTriggerRegister singleton() {
        return SingletonEnum.SINGLETON.instance;
    }

    private enum SingletonEnum {
        SINGLETON;

        private HandTriggerRegister instance;

        SingletonEnum() {
            instance = new HandTriggerRegister();
        }
    }

    //endregion

    public synchronized void register(String code, Action action) {
        this.delegate.put(code, action);
    }

    /**
     * 按code执行
     *
     * @param codes
     */
    public void doTask(String... codes) {
        for (String code : codes) {
            if(StringUtils.isBlank(code)){
                continue;
            }
            if (this.delegate.containsKey(code)) {
                this.delegate.get(code).exec();
            }
        }
    }

    /**
     * 执行全部任务
     */
    public void doTaskAll() {
        for (Map.Entry<String, Action> entry : this.delegate.entrySet()) {
            entry.getValue().exec();
        }
    }
}
