package com.differ.wdgj.api.user.biz.domain.aftersale.load.work.subtask.create;

import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.context.AfterSaleLoadTaskContext;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.work.processor.BaseLoadAfterSaleProcessor;

/**
 * 创建下载子任务抽象实现
 *
 * <AUTHOR>
 * @date 2025/4/10 下午4:57
 */
public abstract class AbstractCreateSubTaskOperator implements ICreateSubTask{
    // region 变量

    /**
     * 上下文
     */
    protected final AfterSaleLoadTaskContext context;

    /**
     * 售后单下载工作任务处理器
     */
    protected final BaseLoadAfterSaleProcessor loadProcessor;

    // endregion

    // region 构造器
    public AbstractCreateSubTaskOperator(AfterSaleLoadTaskContext context, BaseLoadAfterSaleProcessor loadProcessor) {
        this.context = context;
        this.loadProcessor = loadProcessor;
    }
    // endregion

}
