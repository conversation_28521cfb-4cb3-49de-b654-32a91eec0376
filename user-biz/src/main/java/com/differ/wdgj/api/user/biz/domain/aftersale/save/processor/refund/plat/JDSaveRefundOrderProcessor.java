package com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.refund.plat;

import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.AfterSaleSaveContext;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.DbAfterSaleOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.refund.BaseSaveRefundOrderProcessor;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.PolyPlatEnum;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetRefundOrderResponseOrderItem;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.bizfeature.plugins.PlatShopBizEntity;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.bizfeature.plugins.PlatSourceOrderTypeEntity;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.shop.ShopTypeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.shopconfig.ApiAfterSaleTypeEnum;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * 京东 - 保存退货退款单处理器
 *
 * <AUTHOR>
 * @date 2025/4/8 上午10:31
 */
public class JDSaveRefundOrderProcessor extends BaseSaveRefundOrderProcessor {
    //region 构造

    /**
     * 构造
     *
     * @param context 上下文
     */
    public JDSaveRefundOrderProcessor(AfterSaleSaveContext context) {
        super(context);
    }
    //endregion

    /**
     * 获取店铺类型
     *
     * @param orderItem 菠萝派售后单信息
     * @return 店铺类型
     */
    @Override
    protected ShopTypeEnum getShopType(BusinessGetRefundOrderResponseOrderItem orderItem) {
        //下载返回的售后单店铺类型，赋值为下载订单店铺配置的商家类型
        Integer apiDownloadCfgShopType = context.getDownLoadOrderShopConfig().getMerchantType();
        ShopTypeEnum[] AllShopTypeLst = ShopTypeEnum.values();
        ShopTypeEnum matchedType = Arrays.stream(AllShopTypeLst).filter(x -> x.getApiValue().equals(apiDownloadCfgShopType)
                && x.getPlat() == PolyPlatEnum.BUSINESS_JD).findFirst().orElse(null);
        if (matchedType != null) {
            return matchedType;
        }

        return super.getShopType(orderItem);
    }

    /**
     * 获取api售后单类型
     *
     * @param orderItem 菠萝派售后单信息
     * @param dbOrder   数据库订单信息
     * @return api售后单类型
     */
    @Override
    protected ApiAfterSaleTypeEnum getApiAfterSaleType(BusinessGetRefundOrderResponseOrderItem orderItem, DbAfterSaleOrderItem dbOrder, ShopTypeEnum shopType) {
        // 菠萝派逻辑特殊，refundType不返回JH值，直接返回原始值
        // refundtype返回不为空，根据shoptype + refundtype判断api售后类型
        if (StringUtils.isNotEmpty(orderItem.getRefundType())) {
            PlatShopBizEntity curShopTypeRefundTypeLst = this.context.getAfterSalesConfigPlatFeature(shopType.getCode()).getBizList().stream()
                    .filter(b -> b.getBizValue().equalsIgnoreCase(shopType.getCode())).findFirst().orElse(null);
            if (curShopTypeRefundTypeLst != null && StringUtils.isNotEmpty(orderItem.getRefundType())) {
                PlatSourceOrderTypeEntity specialRefundTypes = curShopTypeRefundTypeLst.getSourceOrderTypeList().stream()
                        .filter(t -> StringUtils.isNotEmpty(t.getPolyTypeValue()) && t.getPolyTypeValue().equalsIgnoreCase(orderItem.getRefundType())).findFirst().orElse(null);
                if (specialRefundTypes != null) {
                    return ApiAfterSaleTypeEnum.create(specialRefundTypes.getApiTypeValue());
                }
            }
        }

        //特殊场景外走基类
        return super.getApiAfterSaleType(orderItem, dbOrder, shopType);
    }
}
