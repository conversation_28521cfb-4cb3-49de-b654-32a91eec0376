package com.differ.wdgj.api.user.biz.infrastructure.data.enums;

import com.differ.wdgj.api.component.util.enums.ValueEnum;

/**
 * 业务手动操作转发-业务类型枚举
 * <AUTHOR>
 * @date 2024/10/28 下午5:07
 */
public enum HandBizTransmitTypeEnum implements ValueEnum {
    /**
     * 下载售后单
     */
    GET_AFTER_SALE(1),
    /**
     * 库存同步
     */
    SYNC_STOCK(2),
    /**
     * 新建店铺
     */
    CREATE_SHOP(3),
    /**
     * 更新店铺配置
     */
    UPDATE_SHOP_CONFIG(4),

    /**
     * 补偿下载售后单
     */
    GET_SUPPLER_AFTER_SALE(5),

    ;

    /**
     * 枚举值
     */
    private final int value;

    /**
     * 构造
     * @param value 枚举值
     */
    HandBizTransmitTypeEnum(int value) {
        this.value = value;
    }

    /**
     * 获取枚举值
     *
     * @return 枚举值
     */
    @Override
    public Integer getValue() {
        return value;
    }
}
