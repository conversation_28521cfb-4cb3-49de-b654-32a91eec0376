package com.differ.wdgj.api.component.multidb.aop;

import com.differ.jackyun.framework.component.multidb.constant.LogConstant;
import com.differ.jackyun.framework.component.multidb.context.DbLookupKeyContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 
 * <AUTHOR>
 *
 * @date 2017年11月27日
 *
 * @desc 清空切库相关的线程变量
 */
public class RdsInfoClearInterceptor extends HandlerInterceptorAdapter {

    private final Logger logger = LoggerFactory.getLogger(RdsInfoClearInterceptor.class);

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        // 1. 清空数据源切换标志的线程变量

        String lookupKey = DbLookupKeyContext.getLookupKey();

        DbLookupKeyContext.remove();

        logger.debug("{}{}{}{}",LogConstant.COMMON_PREFIX , "已将rds变量[" , lookupKey , "]清除");

        super.afterCompletion(request, response, handler, ex);
    }

}
