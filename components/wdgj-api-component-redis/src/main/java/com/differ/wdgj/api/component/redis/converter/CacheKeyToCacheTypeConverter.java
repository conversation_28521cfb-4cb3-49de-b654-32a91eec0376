package com.differ.wdgj.api.component.redis.converter;

/**
 * cacheKey转换为cacheType的转换器,多redis库时必须设置映射关系
 * <AUTHOR>
 * @date 2020/12/14 14:44
 */
public interface CacheKeyToCacheTypeConverter {

    /**
     * 默认缓存类型
     * @return
     */
    String defaultType();

    /**
     * cacheKey转换为cacheType
     * @param cacheKey 缓存键
     * @return cacheType
     */
    String toCacheType(String cacheKey);
}
