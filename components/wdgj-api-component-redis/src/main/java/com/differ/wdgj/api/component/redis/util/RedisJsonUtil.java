package com.differ.wdgj.api.component.redis.util;

/**
 * redis工具类json
 *
 * <AUTHOR>
 * @date 2024/1/26 15:29
 */
public interface RedisJsonUtil {

    /**
     * 对象转换为json
     * @param obj
     * @return
     */
    String toJson(Object obj);

    /**
     * json转换为对象
     *
     * @param json json
     * @param cls  转换的类
     * @return json字符串
     */
    <T> T deJson(String json, Class<T> cls);
}
