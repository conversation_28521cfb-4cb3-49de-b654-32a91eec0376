package com.differ.wdgj.api.component.redis.distribution.lock.rw;

import org.springframework.core.io.ClassPathResource;
import org.springframework.data.redis.core.script.DefaultRedisScript;

import java.util.HashMap;
import java.util.Map;

/**
 * 分布式读写锁脚本
 *
 * <AUTHOR>
 * @date 2023/3/5 0005 23:18
 */
public class DistributionRwLockScript {

    // region 单例

    /**
     * 构造方法
     */
    private DistributionRwLockScript() {
    }

    /**
     * 枚举单例
     *
     * @return 单例
     */
    public static DistributionRwLockScript singleton() {
        return SingletonEnum.SINGLETON.instance;
    }

    /**
     * 单例枚举
     */
    private enum SingletonEnum {

        /**
         * 单例
         */
        SINGLETON;

        private final DistributionRwLockScript instance;

        SingletonEnum() {
            DistributionRwLockScript redisScript = new DistributionRwLockScript();
            redisScript.initAllScript();
            instance = redisScript;
        }
    }

    // endregion

    // region 变量 & 常量

    /**
     * lua 脚本对象集合
     */
    private Map<String, DefaultRedisScript<?>> redisScripts;

    /**
     * lua 脚本路径 - 分布式读写锁 - 读锁 - 加锁
     */
    private static final String LUA_RW_LOCK_READ_LOCK = "scripts/distribution/lock/rw/rw_lock_read_lock.lua";

    /**
     * lua 脚本路径 - 分布式读写锁 - 读锁 - 解锁
     */
    private static final String LUA_RW_LOCK_READ_RELEASE = "scripts/distribution/lock/rw/rw_lock_read_release.lua";

    /**
     * lua 脚本路径 - 分布式读写锁 - 写锁 - 加锁
     */
    private static final String LUA_RW_LOCK_WRITE_LOCK = "scripts/distribution/lock/rw/rw_lock_write_lock.lua";

    /**
     * lua 脚本路径 - 分布式读写锁 - 写锁 - 解锁
     */
    private static final String LUA_RW_LOCK_WRITE_RELEASE = "scripts/distribution/lock/rw/rw_lock_write_release.lua";

    // endregion

    // region 公共方法

    /**
     * lua 脚本路径 - 分布式读写锁 - 读锁 - 加锁
     */
    public DefaultRedisScript<?> luaRwLockReadLock() {
        return this.redisScripts.get(LUA_RW_LOCK_READ_LOCK);
    }

    /**
     * lua 脚本路径 - 分布式读写锁 - 读锁 - 解锁
     */
    public DefaultRedisScript<?> luaRwLockReadRelease() {
        return this.redisScripts.get(LUA_RW_LOCK_READ_RELEASE);
    }

    /**
     * lua 脚本路径 - 分布式读写锁 - 写锁 - 加锁
     */
    public DefaultRedisScript<?> luaRwLockWriteLock() {
        return this.redisScripts.get(LUA_RW_LOCK_WRITE_LOCK);
    }

    /**
     * lua 脚本路径 - 分布式读写锁 - 写锁 - 解锁
     */
    public DefaultRedisScript<?> luaRwLockWriteRelease() {
        return this.redisScripts.get(LUA_RW_LOCK_WRITE_RELEASE);
    }


    // endregion

    // region 私有方法

    /**
     * 初始 lua
     */
    protected void initAllScript() {
        this.redisScripts = new HashMap<>();
        this.initScript(LUA_RW_LOCK_READ_LOCK, Integer.class);
        this.initScript(LUA_RW_LOCK_READ_RELEASE, Integer.class);
        this.initScript(LUA_RW_LOCK_WRITE_LOCK, Integer.class);
        this.initScript(LUA_RW_LOCK_WRITE_RELEASE, Integer.class);
    }

    /**
     * 初始单个 lua
     */
    protected <T> void initScript(String script, Class<T> resultType) {
        DefaultRedisScript<T> redisScript = new DefaultRedisScript<>();
        redisScript.setLocation(new ClassPathResource(script));
        redisScript.setResultType(resultType);
        redisScripts.put(script, redisScript);
    }

    // endregion
}