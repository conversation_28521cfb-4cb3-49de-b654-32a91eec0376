-- 支持过期的hash批量查询
-- User: chuff
-- Date: 2021/11/29 14:25
--

local cacheKey = KEYS[1]

-- 缓存键过期时间
local cacheKeyExpire = tonumber(ARGV[1])
-- 当前时间
local currentTime = tonumber(ARGV[2])

-- 缓存键的key后缀
local cacheKeyTail = math.floor(currentTime / cacheKeyExpire)

local cacheKeyWithTail = cacheKey .. cacheKeyTail
local cacheKeyWithBefore = cacheKey .. (cacheKeyTail - 1)

-- 数据格式:{时间戳内容}#{数据版本内容}#{数据内容}
local function analyzeHashValue(data, dataType)
    if dataType == 0 then
        local startIndex = 1
        local endIndex = string.find(data, "#", 1)
        return string.sub(data, startIndex, endIndex - 1)
    end
    if dataType == 1 then
        local startIndex = string.find(data, "#", 1) + 1
        local endIndex = string.find(data, "#", startIndex)
        return string.sub(data, startIndex, endIndex - 1)
    end
    local startIndex = string.find(data, "#", 1) + 1
    startIndex = string.find(data, "#", startIndex) + 1
    return string.sub(data, startIndex, -1)
end

local function hashGet(keyWithTail, keyWithBefore, current, hashField)
    local fromBefore = 0
    -- 查询当前缓存
    local hashValue = redis.call("hget", keyWithTail, hashField) or nil
    if hashValue == nil then
        hashValue = redis.call("hget", keyWithBefore, hashField) or nil
        if hashValue == nil then
            return nil
        end
        fromBefore = 1
    end

    -- 解析过期时间
    local timestamp = tonumber(analyzeHashValue(hashValue, 0))
    if current > timestamp then
        -- 已过期
        if fromBefore == 1 then
            redis.call("hdel", keyWithBefore, hashField)
        else
            redis.call("hdel", keyWithTail, hashField)
        end
        -- 过期时返回空
        return nil;
    end

    -- 返回结果
    return analyzeHashValue(hashValue, 2)

end

local len = #ARGV
local ret = {}
for i = 3, len, 1 do
    ret[i - 2] = hashGet(cacheKeyWithTail, cacheKeyWithBefore, currentTime, ARGV[i])
end
return ret

