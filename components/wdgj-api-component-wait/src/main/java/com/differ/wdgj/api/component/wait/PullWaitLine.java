package com.differ.wdgj.api.component.wait;

import com.differ.wdgj.api.component.wait.data.CommonWaitEntity;
import com.differ.wdgj.api.component.wait.data.PullWaitContext;

import java.util.Map;

/**
 * @Description 排队接口抽象
 * <AUTHOR>
 * @Date 2023/12/6 18:26
 */
public interface PullWaitLine extends WaitLine {
    /**
     * 拉取模式初始化
     *
     * @param context
     * @param strategy
     */
    void initPullMode(PullWaitContext context, PullWaitStrategy strategy);

    /**
     * 拉取下一个排队任务数据,并且处理
     * @return true:取到任务并且处理，false:没有取到任务
     */
    boolean fetchNextAndHandle();

    /**
     * 添加任务到排队队列
     *
     * @param entity
     * @return true:放入排队（新增排队）, false:已存在（排队中或执行中）
     */
    boolean putWait(AbstractWaitEntity entity);

    /**
     * 拉取下一个排队数据
     * @return 返回排队的key和数据
     */
    CommonWaitEntity pullNext();

    /**
     * 完成任务
     * @param waitUniqueId 排队的任务key
     */
    void complete(String waitUniqueId);
}
