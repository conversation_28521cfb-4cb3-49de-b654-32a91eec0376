package com.differ.wdgj.api.component.util.tools;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.Security;
import java.util.Base64;

/**
 * aes算法加解密，对应dotnet的Rijndael.Create()创建的默认加密算法，运算模式：CBC，填充模式：PKCS7
 *
 * <AUTHOR>
 * @date 2021/2/3 11:20
 */
public class AESRijndael {

    private static Logger log = LoggerFactory.getLogger(AESRijndael.class);

    static {
        Security.addProvider(new org.bouncycastle.jce.provider.BouncyCastleProvider());
    }

    //region 常量

    private static final String ENCRYPT_ALG = "AES";

    private static final String CIPHER_MODE = "AES/CBC/PKCS7Padding";

    private static final int SECRET_KEY_SIZE = 32;

    private static final int SECRET_IV_SIZE = 16;

    private Cipher cipherEncrypt;

    private Cipher cipherDecrypt;

    //endregion

    public void init(byte[] realKey, byte[] realIV) {
        if (realKey == null || realKey.length != SECRET_KEY_SIZE) {
            throw new RuntimeException("key参数错误");
        }
        if (realIV == null || realIV.length != SECRET_IV_SIZE) {
            throw new RuntimeException("iv参数错误");
        }
        try {
            Security.addProvider(new org.bouncycastle.jce.provider.BouncyCastleProvider());

            cipherEncrypt = Cipher.getInstance(CIPHER_MODE);
            cipherEncrypt.init(Cipher.ENCRYPT_MODE, new SecretKeySpec(realKey, ENCRYPT_ALG), new IvParameterSpec(realIV));

            cipherDecrypt = Cipher.getInstance(CIPHER_MODE);
            cipherDecrypt.init(Cipher.DECRYPT_MODE, new SecretKeySpec(realKey, ENCRYPT_ALG), new IvParameterSpec(realIV));
        } catch (Exception e) {
            throw new RuntimeException("AESRijndael初始化失败", e);
        }
    }

    /**
     * 加密
     *
     * @param text 待加密的文本
     * @return 加密后的文本
     */
    public String encrypt(String text) {
        try {
            byte[] data = cipherEncrypt.doFinal(text.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(data);
        } catch (Exception e) {
            throw new RuntimeException("AESRijndael加密失败：content=" + text);
        }
    }

    /**
     * 解密
     *
     * @param text 待解密的文本
     * @return 解密后的文本
     */
    public String decrypt(String text) {
        if (StringUtils.isEmpty(text)) {
            throw new RuntimeException("密文不能为空");
        }

        try {
            byte[] decodeBytes = Base64.getDecoder().decode(text);
            byte[] realBytes = cipherDecrypt.doFinal(decodeBytes);
            return new String(realBytes, StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw new RuntimeException("AESRijndael解密失败：" + e.fillInStackTrace(), e);
        }
    }
}
