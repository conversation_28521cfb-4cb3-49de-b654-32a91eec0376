package com.differ.wdgj.api.component.util.tools;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.fasterxml.jackson.module.jaxb.JaxbAnnotationModule;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Xml解析工具类
 * 基于jackson的XmlMapper实现的Xml解析器
 * 常用注解：
 * {@link JacksonXmlRootElement} 定义根元素
 * {@link JacksonXmlProperty} 属性/元素映射
 * {@link JacksonXmlElementWrapper} 集合包装
 * {@link JsonInclude} 包含策略
 *
 * <AUTHOR>
 * @date 2025/8/1 13:33
 */
public class XmlUtils {
    /**
     * 用于存放jaxbContext对象，以避免每次均创建造成性能损耗
     */
    private static final XmlMapper objectMapper = new XmlMapper();

    static {
        // 使用jaxb的注解模式
        objectMapper.registerModule(new JaxbAnnotationModule());

        // 设置转换模式
        objectMapper.enable(MapperFeature.USE_STD_BEAN_NAMING);

        // 序列化配置
        // 使用枚举的重载方法toString输出值
        objectMapper.configure(SerializationFeature.WRITE_ENUMS_USING_TO_STRING, true);
        // 字段为null，自动忽略，不再序列化
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        objectMapper.enable(SerializationFeature.INDENT_OUTPUT);

        // 反序列化配置
        objectMapper.configure(DeserializationFeature.READ_ENUMS_USING_TO_STRING, true);
        objectMapper.configure(DeserializationFeature.READ_UNKNOWN_ENUM_VALUES_AS_NULL, true);
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

        // 忽略大小写
        objectMapper.configure(MapperFeature.ACCEPT_CASE_INSENSITIVE_PROPERTIES, false);

        //注册其他已存在的模块
        objectMapper.findAndRegisterModules();

        //region LocalDatetime时间处理 重新注册时间模块覆盖之前时间模块

        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        // 注册时间处理模块
        JavaTimeModule javaTimeModule = new JavaTimeModule();
        LocalDateTimeDeserializer localDateTimeDeserializer = new LocalDateTimeDeserializer(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        javaTimeModule.addDeserializer(LocalDateTime.class, localDateTimeDeserializer);
        // 注册时间序列化模块
        LocalDateTimeSerializer localDateTimeSerializer = new LocalDateTimeSerializer(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        javaTimeModule.addSerializer(LocalDateTime.class, localDateTimeSerializer);
        objectMapper.registerModule(javaTimeModule);

        //endregion
    }

    // region 公共方法

    /**
     * 将对象转xml
     *
     * @param obj 对象
     * @return xml字符串
     */
    public static String toXml(Object obj) throws Exception {

        if (null == obj) {
            return null;
        }
        if (obj instanceof String) {
            return obj.toString();
        }
        return objectMapper.writeValueAsString(obj);
    }

    /**
     * 将xml转对象
     *
     * @param xml   xml字符串
     * @param clazz 对象类型
     * @return 对象
     */
    public static <T> T deXml(String xml, Class<T> clazz) throws Exception {
        if (StringUtils.isEmpty(xml)) {
            return null;
        }
        return objectMapper.readValue(xml, clazz);
    }
    // endregion

}
