/*
 * Copyright(C) 2017 Hangzhou Differsoft Co., Ltd. All rights reserved.
 */
package com.differ.wdgj.api.component.util.http;

import com.differ.jackyun.framework.component.basic.interceptor.CommonContextHolder;
import com.differ.wdgj.api.component.util.http.core.HttpTlsEnum;
import com.differ.wdgj.api.component.util.http.core.HttpsInstance;
import com.differ.wdgj.api.component.util.http.core.HttpsKey;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;

import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * Http请求工具类
 *
 * <AUTHOR>
 * @date 2024/8/12 14:33
 */
public class HttpTools {
    //region 常量
    private static final Logger LOG = LoggerFactory.getLogger(HttpTools.class);

    /**
     * 默认超时时间(默认90秒)。
     */
    public static final int DEFAULT_TIME_OUT = 90000;
    //endregion

    //region 构造
    private HttpTools() {
    }
    //endregion

    //region Post
    /**
     * Post方式请求
     *
     * @param url      路径
     * @param postData 发送的数据
     * @return HTTP-Post返回结果
     */
    public static String postData(String url, Map<String, String> postData, String httpTlsEnum){
        return postData(url, postData, httpTlsEnum, null);
    }

    /**
     * Post方式请求
     *
     * @param url      路径
     * @param postData 发送的数据
     * @param timeout  超时时间
     * @return HTTP-Post返回结果
     */
    public static String postData(String url, Map<String, String> postData, String httpTlsEnum, Integer timeout) {
        // Post请求超时时间。
        if(timeout == null){
            timeout = DEFAULT_TIME_OUT;
        }

        Map<String, String> headerMap = new HashMap<>();
        // 赋值日志ID
        headerMap.put("loggerSN", CommonContextHolder.getLoggerSn().toString());

        return postData(url, postData, headerMap, httpTlsEnum, timeout, StandardCharsets.UTF_8);
    }

    /**
     * Post方式请求
     *
     * @param url       路径
     * @param postData  发送的数据
     * @param headerMap 头信息集合
     * @param timeOut   超时时间
     * @param encoding  编码格式
     * @return HTTP-Post返回结果
     */
    public static String postData(String url, Map<String, String> postData, Map<String, String> headerMap, String tlsCode, int timeOut, Charset encoding) {
        HttpTlsEnum httpTlsEnum = HttpTlsEnum.convert(tlsCode);
        return postData(url, postData, headerMap, httpTlsEnum, timeOut, encoding);
    }

    /**
     * Post方式请求
     *
     * @param url       路径
     * @param postData  发送的数据
     * @param headerMap 头信息集合
     * @param timeOut   超时时间
     * @param encoding  编码格式
     * @return HTTP-Post返回结果
     */
    public static String postData(String url, Map<String, String> postData, Map<String, String> headerMap, HttpTlsEnum httpTlsEnum, int timeOut, Charset encoding) {
        // 判断超时配置是否低于默认超时时间。
        if (timeOut < DEFAULT_TIME_OUT) {
            timeOut = DEFAULT_TIME_OUT;
        }

        HttpsKey httpsKey = new HttpsKey(httpTlsEnum, timeOut);

        ResponseEntity<byte[]> response = new HttpsInstance(httpsKey).exchange(url, postData, headerMap, encoding, HttpMethod.POST);

        return new String(response.getBody(), encoding);
    }

    /**
     * Post方式请求
     *
     * @param url       路径
     * @param postData  发送的数据
     * @param headerMap 头信息集合
     * @param timeOut   超时时间
     * @param encoding  编码格式
     * @return HTTP-Post返回结果
     */
    public static <T> String postDataCommon(String url, T postData, Map<String, String> headerMap, HttpTlsEnum httpTlsEnum, int timeOut, Charset encoding) {

        // 判断超时配置是否低于默认超时时间。
        if (timeOut < DEFAULT_TIME_OUT) {
            timeOut = DEFAULT_TIME_OUT;
        }

        HttpsKey httpsKey = new HttpsKey(httpTlsEnum, timeOut);

        ResponseEntity<byte[]> response = new HttpsInstance(httpsKey).exchange(url, postData, headerMap, encoding, HttpMethod.POST);

        return new String(response.getBody(), encoding);
    }
    //endregion

}
