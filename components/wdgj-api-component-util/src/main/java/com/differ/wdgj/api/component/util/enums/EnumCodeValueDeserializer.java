package com.differ.wdgj.api.component.util.enums;

import com.alibaba.fastjson.parser.DefaultJSONParser;
import com.alibaba.fastjson.parser.JSONLexer;
import com.alibaba.fastjson.parser.JSONToken;
import com.alibaba.fastjson.parser.deserializer.ObjectDeserializer;

import java.lang.reflect.Type;

/**
 * @Description fastJson枚举值反向序列化器
 * 使用示例：枚举类添加注解：@JSONType(deserializer = EnumCodeValueDeserializer.class)
 * <AUTHOR>
 * @Date 2020-03-18 17:03
 */
public class EnumCodeValueDeserializer implements ObjectDeserializer {
    @Override
    public <T> T deserialze(DefaultJSONParser parser, Type type, Object fieldName) {
        final JSONLexer lexer = parser.lexer;
        final int token = lexer.token();
        String value = null;
        T enumObject = null;
        if (token == JSONToken.LITERAL_INT) {
            value = lexer.numberString();
            lexer.nextToken(JSONToken.COMMA);
            enumObject = (T) EnumConvertCacheUtil.convert(type, value, EnumConvertType.VALUE, EnumConvertType.VALUE);
        } else if (token == JSONToken.LITERAL_STRING) {
            value = lexer.stringVal();
            lexer.nextToken(JSONToken.COMMA);
            enumObject = (T) EnumConvertCacheUtil.convert(type, value, EnumConvertType.CODE, EnumConvertType.NAME, EnumConvertType.VALUE);
        }
        return enumObject;
    }

    @Override
    public int getFastMatchToken() {
        return JSONToken.LITERAL_INT;
    }
}
