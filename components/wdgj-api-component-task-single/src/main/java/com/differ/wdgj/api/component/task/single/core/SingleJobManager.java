package com.differ.wdgj.api.component.task.single.core;

import org.apache.commons.lang3.StringUtils;
import org.quartz.Scheduler;
import org.quartz.impl.triggers.CronTriggerImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * @Description 独立非分布式的定时任务工具类
 * <AUTHOR>
 * @Date 2020/11/24 11:55
 */
@Component
public class SingleJobManager implements ApplicationContextAware {

    private Logger log = LoggerFactory.getLogger(SingleJobManager.class);

    /**
     * 正常执行的任务集合
     */
    private Map<String, JobInfo> mapJobInfo = new HashMap<>();

    /**
     * spring上下文
     */
    private static ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        if (SingleJobManager.applicationContext == null) {
            SingleJobManager.applicationContext = applicationContext;
        }
    }

    public static ApplicationContext getApplicationContext() {
        return applicationContext;
    }

    /**
     * 添加正常运行的任务
     *
     * @param jobName
     * @param jobInfo
     */
    public void addJobInfo(String jobName, JobInfo jobInfo) {
        if (StringUtils.isEmpty(jobName) || jobInfo == null) {
            throw new RuntimeException("添加单体任务参数错误");
        }
        mapJobInfo.putIfAbsent(jobName, jobInfo);
    }

    /**
     * 获取正常运行的任务
     *
     * @param jobName
     * @return
     */
    public JobInfo getJobInfo(String jobName) {
        if (StringUtils.isBlank(jobName)) {
            return null;
        }
        return mapJobInfo.get(jobName);
    }

    /**
     * 更新任务的cron
     * @param jobName 任务名
     * @param cornToUpdate 新的cron
     */
    public void updateJobCorn(String jobName,String cornToUpdate) {
        try {
            JobInfo jobInfo = getJobInfo(jobName);
            if(jobInfo == null){
                log.warn("未找到单点定时任务:{}",jobName);
                return;
            }

            CronTriggerImpl trigger = (CronTriggerImpl) jobInfo.getTrigger();
            String cronCurrent = trigger.getCronExpression();
            // 判断corn是否有变更
            if (StringUtils.isNotBlank(cornToUpdate) && !cronCurrent.equalsIgnoreCase(cornToUpdate)) {
                //记录下次执行时间
                Date nextFireTime = trigger.getNextFireTime();
                // 设置cron表达式
                trigger.setCronExpression(cornToUpdate);
                //不立即执行,设置下次执行时间
                trigger.setStartTime(nextFireTime);
                // 按新的trigger重新设置job执行
                applicationContext.getBean(Scheduler.class).rescheduleJob(trigger.getKey(), trigger);
                log.info("单点任务{},corn从{}更新为{}成功,下次执行时间:{}",jobName,cronCurrent,cornToUpdate,nextFireTime);
            }
        }
        catch (ParseException parseException) {
            log.error(String.format("更新corn表达式，转换错误：%s", cornToUpdate),parseException);
        }
        catch (Exception ex) {
            log.error(String.format("更新corn表达式失败：%s", cornToUpdate),ex);
        }
    }
}
