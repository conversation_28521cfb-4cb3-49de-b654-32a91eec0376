package com.differ.wdgj.api.component.task.single.core;

/**
 * @Description 时间间隔执行策略
 * <AUTHOR>
 * @Date 2021/11/4 10:58
 */
public class JobExecTimeAtFrequencyStrategy extends AbstractExecTimeReEntryStrategy {

    /**
     * 执行频率，单位毫秒
     */
    protected long runFrequency = 3000;

    /**
     * 根据传入时间，判断是否可以执行任务
     *
     * @param currentTimeMillis 当时任务的时间戳(毫秒)
     * @return true:可以执行
     */
    @Override
    protected boolean enableRun (long currentTimeMillis) {
        long nextTime = lastRunTime + runFrequency;
        if (currentTimeMillis < nextTime) {
            return false;
        }
        this.lastRunTime = currentTimeMillis;
        return true;
    }

    public void setRunFrequency(long runFrequency) {
        this.runFrequency = runFrequency;
    }
}
