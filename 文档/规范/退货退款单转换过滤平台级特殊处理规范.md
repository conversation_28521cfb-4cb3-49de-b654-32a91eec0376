# 退货退款单转换过滤平台级特殊处理规范

## 1. 概述

本规范文档定义了退货退款单转换过滤过程中平台级特殊处理的标准实现方式，旨在规范不同平台特殊处理的开发流程，确保代码的一致性、可维护性和可扩展性。本规范适用于所有需要为特定电商平台实现退货退款单特殊处理逻辑的开发人员。

## 2. 适用范围

本规范适用于以下场景：

1. 为新平台添加退货退款单处理支持
2. 修改现有平台的退货退款单处理逻辑
3. 为现有平台添加新的特殊处理功能
4. 对现有平台特殊处理逻辑进行优化或修复

## 3. 平台级特殊处理扩展点

退货退款单处理流程中提供了以下平台级特殊处理扩展点：

### 3.1 前置批量处理扩展点

- **类名**：`RefundPlatSpecialBatchHandle`
- **目录**：`user-biz/src/main/java/com/differ/wdgj/api/user/biz/domain/aftersale/save/processor/refund/batch`
- **接口方法**：`perBatchQueryProcess`
- **用途**：用于批量处理阶段的平台特殊逻辑，如批量查询平台特定数据

### 3.2 前置过滤扩展点

- **类名**：`RefundPlatSpecialFilterHandle`
- **目录**：`user-biz/src/main/java/com/differ/wdgj/api/user/biz/domain/aftersale/save/processor/refund/filter`
- **接口方法**：`preFiltrationOrder`
- **用途**：用于前置过滤阶段的平台特殊逻辑，如平台特定的售后单过滤规则

### 3.3 订单级转换扩展点

- **类名**：`RefundPlatSpecialOrderCovertHandle`
- **目录**：`user-biz/src/main/java/com/differ/wdgj/api/user/biz/domain/aftersale/save/processor/refund/convert/order`
- **接口方法**：`convertOrder`
- **用途**：用于订单级转换阶段的平台特殊逻辑，如平台特定字段的处理

### 3.4 商品级转换扩展点

- **类名**：`RefundGoodsPlatSpecialHandle`
- **目录**：`user-biz/src/main/java/com/differ/wdgj/api/user/biz/domain/aftersale/save/processor/refund/convert/goods`
- **接口方法**：`convertGoods`
- **用途**：用于商品级转换阶段的平台特殊逻辑，如平台特定商品属性的处理

### 3.5 后置处理扩展点

- **类名**：`RefundPlatSpecialPostProcessHandle`
- **目录**：`user-biz/src/main/java/com/differ/wdgj/api/user/biz/domain/aftersale/save/processor/refund/convert/post`
- **接口方法**：`processOrder`
- **用途**：用于后置处理阶段的平台特殊逻辑，如平台特定的通知生成

## 4. 平台特定处理实现规范

### 4.1 实现方式选择

平台特定处理分为两种实现方式：

1. **直接重写基类方法**：适用于简单的平台特定逻辑，如简单的字段映射或条件判断
2. **新增插件**：适用于复杂且具备通用性的逻辑，如需要在多个平台中复用的功能

选择原则：
- 如果逻辑简单且仅适用于单一平台，选择直接重写基类方法
- 如果逻辑复杂或需要在多个平台中复用，选择新增插件

### 4.2 命名规范

1. 平台特定处理器类名应遵循 `{平台名称}SaveRefundOrderProcessor` 格式，如 `TmallGJZYSaveRefundOrderProcessor`
2. 平台特定插件类名应遵循 `{平台名称}{功能名称}Handle` 格式，如 `TmallGJZYSpecialMatchHandle`

### 4.3 目录结构规范

- 平台特定处理器应放置在：
```
user-biz/src/main/java/com/differ/wdgj/api/user/biz/domain/aftersale/save/processor/refund/plat
```

- 平台特定插件应放置在对应功能的目录下，如：
```
user-biz/src/main/java/com/differ/wdgj/api/user/biz/domain/aftersale/save/processor/refund/batch  // 批量处理插件
user-biz/src/main/java/com/differ/wdgj/api/user/biz/domain/aftersale/save/processor/refund/filter  // 过滤器插件
user-biz/src/main/java/com/differ/wdgj/api/user/biz/domain/aftersale/save/processor/refund/convert/post  // 后置处理插件
```

### 4.4 基类可重写方法

`BaseSaveRefundOrderProcessor`提供了以下可重写的方法：

| 方法名 | 用途 | 重写场景 | 实现方式建议 |
|------------|------|------------|---------------|
| `createPerBatchQueryHandles()` | 创建前置批量处理插件列表 | 需要添加平台特定的批量处理插件 | 新增插件 |
| `createPreFiltrationHandles()` | 创建前置过滤插件列表 | 需要添加平台特定的过滤插件 | 新增插件 |
| `createOrderConvertsHandles()` | 创建订单级转换插件列表 | 需要添加平台特定的订单转换插件 | 新增插件 |
| `createRefundGoodsConvertsHandles()` | 创建商品转换插件列表 | 需要添加平台特定的商品转换插件 | 新增插件 |
| `createPostProcessHandles()` | 创建后置处理插件列表 | 需要添加平台特定的后置处理插件 | 新增插件 |
| `preBatchOrder()` | 前置批量处理 | 简单的平台特定批量处理逻辑 | 直接重写 |
| `preFiltrationOrder()` | 前置过滤 | 简单的平台特定过滤逻辑 | 直接重写 |
| `orderConvert()` | 订单级转换 | 简单的平台特定订单转换逻辑 | 直接重写 |
| `refundGoodsConvert()` | 商品转换 | 简单的平台特定商品转换逻辑 | 直接重写 |
| `postProcess()` | 后置处理 | 简单的平台特定后置处理逻辑 | 直接重写 |

### 4.5 代码实现规范

#### 4.5.1 直接重写基类方法（简单逻辑）

基本结构：

```java
public class {平台名称}SaveRefundOrderProcessor extends BaseSaveRefundOrderProcessor {
    
    public {平台名称}SaveRefundOrderProcessor(AfterSaleSaveContext context) {
        super(context);
    }
    
    // 重写需要特殊处理的方法
    @Override
    protected AfterSaleHandleResult orderConvert(SourceAfterSaleOrderItem<BusinessGetRefundOrderResponseOrderItem> sourceOrder, TargetCovertOrderItem targetOrder) {
        // 调用父类方法
        AfterSaleHandleResult result = super.orderConvert(sourceOrder, targetOrder);
        if (result.isFailed()) {
            return result;
        }
        
        // 简单的平台特定逻辑
        // ...
        
        return AfterSaleHandleResult.success();
    }
}
```

示例（TmallGJZYSaveRefundOrderProcessor）：

```java
public class TmallGJZYSaveRefundOrderProcessor extends BaseSaveRefundOrderProcessor {
    @Override
    protected AfterSaleHandleResult orderConvert(SourceAfterSaleOrderItem<BusinessGetRefundOrderResponseOrderItem> sourceOrder, TargetCovertOrderItem targetOrder) {
        // 基础数据
        BusinessGetRefundOrderResponseOrderItem ployOrder = sourceOrder.getPloyOrder();
        ApiReturnListDO afterSaleOrder = targetOrder.getAfterSaleOrder();

        // 保存业务类型
        afterSaleOrder.setOrderType(ployOrder.getShopType());

        return AfterSaleHandleResult.success();
    }
}
```

#### 4.5.2 新增插件（复杂逻辑）

基本结构：

```java
public class {平台名称}{功能名称}Handle extends Abstract{功能类型}Handle<BusinessGetRefundOrderResponseOrderItem> {
    
    public {平台名称}{功能名称}Handle(AfterSaleSaveContext context) {
        super(context);
    }
    
    // 实现具体功能方法
    @Override
    protected AfterSaleHandleResult {具体功能方法}({方法参数}) {
        // 复杂的平台特定逻辑
        // ...
        
        return AfterSaleHandleResult.success();
    }
    
    @Override
    protected String caption() {
        return "平台特定功能描述";
    }
}
```

在平台处理器中添加插件：

```java
public class {平台名称}SaveRefundOrderProcessor extends BaseSaveRefundOrderProcessor {
    
    @Override
    protected List<I{功能类型}Handle<BusinessGetRefundOrderResponseOrderItem>> create{功能类型}Handles() {
        List<I{功能类型}Handle<BusinessGetRefundOrderResponseOrderItem>> handles = super.create{功能类型}Handles();
        // 添加平台特定插件
        handles.add(new {平台名称}{功能名称}Handle(context));
        return handles;
    }
}
```

## 5. 扩展点使用规范

### 5.1 前置批量处理扩展

通过重写`preBatchOrder`方法实现简单的平台特定批量处理逻辑：

```java
@Override
protected AfterSaleHandleResult preBatchOrder(List<SourceAfterSaleOrderItem<BusinessGetRefundOrderResponseOrderItem>> orderItems) {
    // 空校验
    if (CollectionUtils.isEmpty(orderItems)) {
        return AfterSaleHandleResult.success();
    }

    // 平台特定批量处理逻辑
    // ...

    return AfterSaleHandleResult.success();
}
```

或通过重写`createPerBatchQueryHandles`方法添加平台特定的批量处理器：

```java
@Override
protected List<IPerBatchProcessOrderHandle<BusinessGetRefundOrderResponseOrderItem>> createPerBatchQueryHandles() {
    List<IPerBatchProcessOrderHandle<BusinessGetRefundOrderResponseOrderItem>> perBatchQueryHandles = super.createPerBatchQueryHandles();
    // 添加平台特定批量处理器
    perBatchQueryHandles.add(new PlatformSpecificBatchQueryHandle(context));
    return perBatchQueryHandles;
}
```

### 5.2 前置过滤扩展

通过重写`preFiltrationOrder`方法实现平台特定的过滤逻辑：

```java
@Override
protected AfterSaleHandleResult preFiltrationOrder(SourceAfterSaleOrderItem<BusinessGetRefundOrderResponseOrderItem> orderItem) {
    // 平台特定过滤逻辑
    if (特定条件) {
        return AfterSaleHandleResult.failed("平台特定过滤原因");
    }
    return AfterSaleHandleResult.success();
}
```

### 5.3 订单级转换扩展

通过重写`orderConvert`方法实现平台特定的订单转换逻辑：

```java
@Override
protected AfterSaleHandleResult orderConvert(SourceAfterSaleOrderItem<BusinessGetRefundOrderResponseOrderItem> sourceOrder, TargetCovertOrderItem targetOrder) {
    // 调用父类方法
    AfterSaleHandleResult result = super.orderConvert(sourceOrder, targetOrder);
    if (result.isFailed()) {
        return result;
    }
    
    // 平台特定订单转换逻辑
    // ...
    
    return AfterSaleHandleResult.success();
}
```

### 5.4 商品级转换扩展

通过重写`refundGoodsConvert`方法实现平台特定的商品转换逻辑：

```java
@Override
protected GoodsConvertHandleResult refundGoodsConvert(SourceAfterSaleOrderItem<BusinessGetRefundOrderResponseOrderItem> sourceOrder, SourceRefundGoodsItem<BusinessGetRefundResponseRefundGoodInfo> sourceGoods, TargetCovertOrderItem targetOrder, ApiReturnDetailDO refundGoods) {
    // 平台特定商品转换逻辑
    // ...
    
    return GoodsConvertHandleResult.success();
}
```

### 5.5 后置处理扩展

通过重写`postProcess`方法实现平台特定的后置处理逻辑：

```java
@Override
protected AfterSaleHandleResult postProcess(SourceAfterSaleOrderItem<BusinessGetRefundOrderResponseOrderItem> sourceOrder, TargetCovertOrderItem targetOrder) {
    // 调用父类方法
    AfterSaleHandleResult result = super.postProcess(sourceOrder, targetOrder);
    if (result.isFailed()) {
        return result;
    }
    
    // 平台特定后置处理逻辑
    // ...
    
    return AfterSaleHandleResult.success();
}
```

## 6. 平台特殊处理开发流程

### 6.1 需求分析

1. 明确平台特殊处理的具体需求
2. 确定需要使用的扩展点
3. 分析现有代码，确保不重复实现已有功能

### 6.2 开发步骤

1. 创建平台特定处理器类或扩展现有处理器
2. 实现必要的特殊处理逻辑
3. 添加详细的注释和日志
4. 进行单元测试，确保功能正常

### 6.3 代码审查

1. 确保代码符合本规范
2. 检查异常处理是否完善
3. 验证日志记录是否充分
4. 确认没有重复实现已有功能

### 6.4 测试验证

1. 单元测试：验证特殊处理逻辑的正确性
2. 集成测试：验证与其他组件的交互
3. 系统测试：验证在实际环境中的表现

## 7. 常见平台特殊处理示例

### 7.1 天猫国际直营平台

```java
public class TmallGJZYSaveRefundOrderProcessor extends BaseSaveRefundOrderProcessor {
    @Override
    protected AfterSaleHandleResult orderConvert(SourceAfterSaleOrderItem<BusinessGetRefundOrderResponseOrderItem> sourceOrder, TargetCovertOrderItem targetOrder) {
        // 基础数据
        BusinessGetRefundOrderResponseOrderItem ployOrder = sourceOrder.getPloyOrder();
        ApiReturnListDO afterSaleOrder = targetOrder.getAfterSaleOrder();

        // 保存业务类型
        afterSaleOrder.setOrderType(ployOrder.getShopType());

        return AfterSaleHandleResult.success();
    }
}
```

### 7.2 抖店超市平台

```java
public class DouDianSupermarketSaveRefundOrderProcessor extends BaseSaveRefundOrderProcessor {
    @Override
    protected AfterSaleHandleResult preBatchOrder(List<SourceAfterSaleOrderItem<BusinessGetRefundOrderResponseOrderItem>> orderItems) {
        // 空校验
        if (CollectionUtils.isEmpty(orderItems)) {
            return AfterSaleHandleResult.success();
        }

        // 抖店超市特殊处理
        for (SourceAfterSaleOrderItem<BusinessGetRefundOrderResponseOrderItem> orderItem : orderItems) {
            BusinessGetRefundOrderResponseOrderItem ployOrder = orderItem.getPloyOrder();
            // 特殊处理逻辑
            // ...
        }

        return AfterSaleHandleResult.success();
    }
}
```

## 8. 注意事项

1. **避免硬编码**：不要在代码中硬编码平台特定的常量，应使用配置或枚举
2. **保持向后兼容**：修改现有平台处理逻辑时，应确保向后兼容
3. **性能考虑**：平台特殊处理不应引入性能瓶颈
4. **代码复用**：尽量复用现有代码，避免重复实现
5. **测试覆盖**：确保所有平台特殊处理逻辑都有充分的测试覆盖
6. **文档更新**：实现新的平台特殊处理后，应更新相关文档

## 9. 常见问题与解决方案

### 9.1 如何确定需要实现哪些特殊处理？

分析平台API文档和现有订单数据，确定平台特有的字段和处理逻辑。与业务人员沟通，了解平台特有的业务规则。

### 9.2 如何处理平台API变更？

1. 分析API变更的影响范围
2. 修改相应的平台特殊处理逻辑
3. 确保向后兼容，处理历史数据
4. 充分测试，确保变更不会影响现有功能

### 9.3 如何处理多平台共性的特殊处理？

如果多个平台有共性的特殊处理逻辑，应考虑将其提取到基类或通用工具类中，避免代码重复。

## 10. 参考资料

1. 退货退款单过滤转换逻辑分析文档
2. 售后单保存流程设计文档
3. 各平台API文档

## 11. 附录

### 11.1 平台特殊处理清单

| 平台 | 处理器类名 | 特殊处理内容 |
|------|------------|------------|
| 天猫国际直营 | TmallGJZYSaveRefundOrderProcessor | 业务类型设置 |
| 抖店超市 | DouDianSupermarketSaveRefundOrderProcessor | 售后单号处理 |
| 得物 | DeWuSaveRefundOrderProcessor | 密文处理 |
| 小红书 | XiaoHSSaveRefundOrderProcessor | 待补充 |

### 11.2 常用工具类

| 工具类名 | 用途 |
|----------|------|
| AfterSaleCovertUtils | 售后单转换通用工具 |
| AfterSaleGoodsMatchOperation | 售后商品匹配操作 |
| AfterSaleLogUtils | 售后单日志工具 |
| DownloadOrderShopConfigUtils | 店铺配置工具 |
